#!/usr/bin/env python3
"""
<PERSON>ript to convert <PERSON><PERSON><PERSON> notebook to HTML without code cells - outputs only
"""

import json
import base64
import re
from pathlib import Path

def convert_notebook_to_clean_html(notebook_path, output_path):
    """Convert Jupyter notebook to HTML with only outputs (no code cells)"""
    
    # Read the notebook
    with open(notebook_path, 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    html_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CA Hospital Revenue Cycle Management Analysis Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }
        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
            margin-top: 10px;
        }
        .output-section {
            margin-bottom: 30px;
            padding: 20px;
            border-left: 4px solid #3498db;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .output-text {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #27ae60;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .chart-container {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .chart-container img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .section-title {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-top: 30px;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        .analysis-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .metric-highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .insight-box {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .toc {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .toc h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
        }
        .toc a {
            color: #3498db;
            text-decoration: none;
        }
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>CA Hospital Revenue Cycle Management Analysis</h1>
            <div class="subtitle">Executive Summary Report</div>
            <div class="subtitle">Analysis Date: September 9, 2025</div>
        </div>

        <div class="toc">
            <h3>📋 Table of Contents</h3>
            <ul>
                <li><a href="#overview">Analysis Overview</a></li>
                <li><a href="#financial">Financial Performance</a></li>
                <li><a href="#claims">Claims Processing</a></li>
                <li><a href="#denials">Denial Analysis</a></li>
                <li><a href="#payers">Payer Performance</a></li>
                <li><a href="#trends">Temporal Trends</a></li>
                <li><a href="#recommendations">Strategic Recommendations</a></li>
            </ul>
        </div>
"""
    
    section_counter = 0
    chart_counter = 0
    
    # Process each cell
    for cell in notebook['cells']:
        if cell['cell_type'] == 'code':
            # Only process outputs, skip code
            if cell.get('outputs'):
                has_meaningful_output = False
                output_html = ""
                
                for output in cell['outputs']:
                    if output.get('output_type') == 'stream':
                        # Text output
                        text = ''.join(output.get('text', []))
                        if text.strip() and len(text.strip()) > 10:  # Only meaningful outputs
                            has_meaningful_output = True
                            output_html += f"""
                <div class="output-text">{text}</div>
"""
                    elif output.get('output_type') == 'display_data':
                        # Image output
                        if 'data' in output and 'image/png' in output['data']:
                            chart_counter += 1
                            img_data = output['data']['image/png']
                            has_meaningful_output = True
                            output_html += f"""
                <div class="chart-container">
                    <h4>Chart {chart_counter}: Analysis Visualization</h4>
                    <img src="data:image/png;base64,{img_data}" alt="Analysis Chart {chart_counter}">
                </div>
"""
                
                if has_meaningful_output:
                    section_counter += 1
                    html_content += f"""
        <div class="output-section" id="section-{section_counter}">
{output_html}
        </div>
"""
        
        elif cell['cell_type'] == 'markdown':
            # Add markdown cell as section headers
            if cell.get('source'):
                markdown_content = ''.join(cell['source'])
                if markdown_content.strip():
                    html_content += f"""
        <div class="section-title">{markdown_content}</div>
"""
    
    # Add executive summary
    html_content += """
        <div class="analysis-summary">
            <h2>📊 Executive Summary</h2>
            <p><strong>Analysis Period:</strong> January 2025 - May 2025</p>
            <p><strong>Total Records Analyzed:</strong> 70,000 billing transactions</p>
            <p><strong>Total Revenue:</strong> $224.8 Million</p>
            <p><strong>Collection Rate:</strong> 59.3%</p>
            <p><strong>Key Opportunity:</strong> $32M potential annual revenue increase through process improvements</p>
        </div>

        <div class="insight-box">
            <h3>🎯 Key Insights</h3>
            <ul>
                <li><strong>Denial Management:</strong> 8.6% denial rate with top causes being wrong payer (20%) and missing authorization (18%)</li>
                <li><strong>Payer Performance:</strong> Medicare shows highest approval rate at 91.7%</li>
                <li><strong>Revenue Opportunity:</strong> Improving collection rate to industry benchmark (68%) could increase revenue by $19.5M annually</li>
                <li><strong>Process Improvements:</strong> Real-time eligibility verification and enhanced prior authorization tracking are priority initiatives</li>
            </ul>
        </div>
"""
    
    # Close HTML
    html_content += """
        <div style="text-align: center; margin-top: 40px; padding: 20px; border-top: 2px solid #3498db; color: #7f8c8d;">
            <p><strong>Report Generated:</strong> September 9, 2025</p>
            <p><strong>Analysis Period:</strong> January 2025 - May 2025</p>
            <p><strong>Data Source:</strong> CA Hospital Revenue Cycle Management System</p>
            <p><strong>Charts Included:</strong> """ + str(chart_counter) + """ visualizations</p>
        </div>
    </div>
</body>
</html>"""
    
    # Write the HTML file
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"Successfully converted {notebook_path} to clean HTML: {output_path}")
    print(f"Included {chart_counter} charts and {section_counter} output sections")

if __name__ == "__main__":
    notebook_path = "Untitled-1.ipynb"
    output_path = "CA_Hospital_RCM_Clean_Report.html"
    convert_notebook_to_clean_html(notebook_path, output_path)
