#!/usr/bin/env python3
"""
Script to convert <PERSON><PERSON><PERSON> notebook to HTML with embedded charts
"""

import json
import base64
import re
from pathlib import Path

def convert_notebook_to_html(notebook_path, output_path):
    """Convert <PERSON>pyter notebook to HTML with embedded images"""
    
    # Read the notebook
    with open(notebook_path, 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    html_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CA Hospital Revenue Cycle Management Analysis Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }
        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
            margin-top: 10px;
        }
        .cell {
            margin-bottom: 30px;
            padding: 15px;
            border-left: 4px solid #3498db;
            background-color: #f8f9fa;
        }
        .code-cell {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .output-cell {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #27ae60;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
        }
        .chart-container {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .chart-container img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
        }
        h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h3 {
            color: #34495e;
            margin-top: 25px;
        }
        .metric-card {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            margin: 10px;
            border-radius: 10px;
            text-align: center;
            min-width: 200px;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .toc {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .toc h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
        }
        .toc a {
            color: #3498db;
            text-decoration: none;
        }
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>CA Hospital Revenue Cycle Management Analysis</h1>
            <div class="subtitle">Comprehensive Data Analysis Report</div>
            <div class="subtitle">Analysis Date: September 9, 2025</div>
        </div>
"""
    
    # Process each cell
    for cell in notebook['cells']:
        if cell['cell_type'] == 'code':
            # Add code cell
            if cell.get('source'):
                source_code = ''.join(cell['source'])
                html_content += f"""
        <div class="cell">
            <div class="code-cell">
{source_code}
            </div>
"""
            
            # Add outputs
            if cell.get('outputs'):
                for output in cell['outputs']:
                    if output.get('output_type') == 'stream':
                        # Text output
                        text = ''.join(output.get('text', []))
                        html_content += f"""
            <div class="output-cell">
{text}
            </div>
"""
                    elif output.get('output_type') == 'display_data':
                        # Image output
                        if 'data' in output and 'image/png' in output['data']:
                            img_data = output['data']['image/png']
                            html_content += f"""
            <div class="chart-container">
                <img src="data:image/png;base64,{img_data}" alt="Chart">
            </div>
"""
            html_content += "        </div>\n"
        
        elif cell['cell_type'] == 'markdown':
            # Add markdown cell
            if cell.get('source'):
                markdown_content = ''.join(cell['source'])
                html_content += f"""
        <div class="cell">
{markdown_content}
        </div>
"""
    
    # Close HTML
    html_content += """
        <div style="text-align: center; margin-top: 40px; padding: 20px; border-top: 2px solid #3498db; color: #7f8c8d;">
            <p><strong>Report Generated:</strong> September 9, 2025</p>
            <p><strong>Analysis Period:</strong> January 2025 - May 2025</p>
            <p><strong>Data Source:</strong> CA Hospital Revenue Cycle Management System</p>
        </div>
    </div>
</body>
</html>"""
    
    # Write the HTML file
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"Successfully converted {notebook_path} to {output_path}")

if __name__ == "__main__":
    notebook_path = "Untitled-1.ipynb"
    output_path = "CA_Hospital_RCM_Complete_Report.html"
    convert_notebook_to_html(notebook_path, output_path)
