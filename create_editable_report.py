#!/usr/bin/env python3
"""
Script to create an editable, interactive HTML report from <PERSON>py<PERSON> notebook
"""

import json
import base64
import re
from pathlib import Path

def create_editable_report(notebook_path, output_path):
    """Create an editable, interactive HTML report"""
    
    # Read the notebook
    with open(notebook_path, 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    html_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CA Hospital RCM Analysis - Editable Report</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #2d3748;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .toolbar {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .toolbar button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: transform 0.2s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .toolbar button:hover {
            transform: scale(1.05);
        }
        
        .toolbar button.active {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .report-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }
        
        .edit-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #10b981;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            display: none;
        }
        
        .editable {
            position: relative;
            transition: all 0.3s ease;
        }
        
        .editable:hover {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 5px;
        }
        
        .editable.editing {
            background: rgba(255, 255, 255, 0.2);
            border: 2px dashed #667eea;
            border-radius: 8px;
            padding: 10px;
        }
        
        .editable[contenteditable="true"] {
            outline: 2px solid #667eea;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 10px;
        }
        
        .report-title {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 15px;
        }
        
        .report-subtitle {
            font-size: 1.3rem;
            color: #64748b;
            margin-bottom: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .stat-label {
            font-size: 1rem;
            color: #64748b;
            font-weight: 500;
        }
        
        .content-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            position: relative;
        }
        
        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            font-size: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .section-content {
            padding: 30px;
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            text-align: center;
            position: relative;
        }
        
        .chart-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .chart-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .output-text {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9rem;
            line-height: 1.6;
            white-space: pre-wrap;
            overflow-x: auto;
            color: #374151;
        }
        
        .comment-box {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            position: relative;
        }
        
        .comment-box::before {
            content: "💬 Comment";
            position: absolute;
            top: -10px;
            left: 15px;
            background: #f59e0b;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
        }
        
        .add-comment-btn {
            background: #f59e0b;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 0.9rem;
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .note-section {
            background: #e0f2fe;
            border: 1px solid #0284c7;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .note-title {
            font-weight: 600;
            color: #0284c7;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .save-indicator {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: #10b981;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            display: none;
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .toolbar {
                position: relative;
                top: auto;
                right: auto;
                margin-bottom: 20px;
                justify-content: center;
            }
            
            .report-title {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="toolbar">
        <button onclick="toggleEditMode()" id="editBtn">
            <i class="fas fa-edit"></i>
            Edit Mode
        </button>
        <button onclick="addComment()" id="commentBtn">
            <i class="fas fa-comment"></i>
            Add Comment
        </button>
        <button onclick="addNote()" id="noteBtn">
            <i class="fas fa-sticky-note"></i>
            Add Note
        </button>
        <button onclick="exportReport()" id="exportBtn">
            <i class="fas fa-download"></i>
            Export
        </button>
        <button onclick="saveReport()" id="saveBtn">
            <i class="fas fa-save"></i>
            Save
        </button>
    </div>

    <div class="save-indicator" id="saveIndicator">
        <i class="fas fa-check"></i>
        Changes saved!
    </div>

    <div class="main-container">
        <div class="report-header">
            <div class="edit-indicator" id="editIndicator">Edit Mode Active</div>
            <h1 class="report-title editable" data-field="title">CA Hospital Revenue Cycle Management</h1>
            <p class="report-subtitle editable" data-field="subtitle">Comprehensive Analysis & Strategic Insights</p>
            <p class="report-date editable" data-field="date">
                <i class="fas fa-calendar-alt"></i>
                Analysis Period: January 2025 - May 2025
            </p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-value editable" data-field="revenue">$224.8M</div>
                <div class="stat-label editable" data-field="revenue-label">Total Revenue Analyzed</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-value editable" data-field="collection">59.3%</div>
                <div class="stat-label editable" data-field="collection-label">Collection Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-value editable" data-field="denial">8.6%</div>
                <div class="stat-label editable" data-field="denial-label">Claims Denial Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-target"></i>
                </div>
                <div class="stat-value editable" data-field="opportunity">$32M</div>
                <div class="stat-label editable" data-field="opportunity-label">Revenue Opportunity</div>
            </div>
        </div>
"""
    
    return html_content

if __name__ == "__main__":
    notebook_path = "Untitled-1.ipynb"
    output_path = "CA_Hospital_Editable_Report.html"
    
    # Start creating the editable report
    html_start = create_editable_report(notebook_path, output_path)
    
    # Read the notebook and extract content
    with open(notebook_path, 'r', encoding='utf-8') as f:
        notebook = json.load(f)

    # Process notebook content
    content_sections = []
    chart_counter = 0

    for cell in notebook['cells']:
        if cell['cell_type'] == 'code' and cell.get('outputs'):
            for output in cell['outputs']:
                if output.get('output_type') == 'stream':
                    text = ''.join(output.get('text', []))
                    if text.strip() and len(text.strip()) > 20:
                        content_sections.append(('text', text))
                elif output.get('output_type') == 'display_data':
                    if 'data' in output and 'image/png' in output['data']:
                        chart_counter += 1
                        img_data = output['data']['image/png']
                        content_sections.append(('chart', img_data, chart_counter))

    # Add content sections to HTML
    content_html = ""
    section_titles = [
        ("overview", "Analysis Overview", "fas fa-chart-bar"),
        ("financial", "Financial Performance", "fas fa-coins"),
        ("claims", "Claims Processing", "fas fa-file-medical"),
        ("denials", "Denial Analysis", "fas fa-times-circle"),
        ("payers", "Payer Performance", "fas fa-building"),
        ("recommendations", "Strategic Recommendations", "fas fa-lightbulb")
    ]

    content_index = 0
    items_per_section = max(1, len(content_sections) // len(section_titles))

    for section_id, section_title, icon in section_titles:
        content_html += f'''
        <div class="content-section" id="{section_id}">
            <div class="section-header editable" data-field="section-{section_id}">
                <i class="{icon}"></i>
                {section_title}
            </div>
            <div class="section-content">
                <div class="note-section">
                    <div class="note-title">
                        <i class="fas fa-info-circle"></i>
                        Section Notes
                    </div>
                    <div class="editable" data-field="notes-{section_id}" contenteditable="false">
                        Click to add your analysis notes and insights for this section...
                    </div>
                </div>
'''

        # Add content for this section
        for i in range(items_per_section):
            if content_index < len(content_sections):
                content_type, *content_data = content_sections[content_index]

                if content_type == 'text':
                    content_html += f'''
                <div class="output-text editable" data-field="output-{content_index}">
                    {content_data[0]}
                </div>
                <button class="add-comment-btn" onclick="addCommentToElement(this)">
                    <i class="fas fa-comment-plus"></i>
                    Add Comment
                </button>
'''
                elif content_type == 'chart':
                    img_data, chart_num = content_data
                    content_html += f'''
                <div class="chart-container">
                    <div class="chart-title editable" data-field="chart-title-{chart_num}">
                        <i class="fas fa-chart-area"></i>
                        Analysis Visualization {chart_num}
                    </div>
                    <img src="data:image/png;base64,{img_data}" alt="Chart {chart_num}">
                    <div class="note-section">
                        <div class="note-title">
                            <i class="fas fa-chart-line"></i>
                            Chart Analysis
                        </div>
                        <div class="editable" data-field="chart-analysis-{chart_num}" contenteditable="false">
                            Add your interpretation and insights about this chart...
                        </div>
                    </div>
                </div>
'''
                content_index += 1

        content_html += '''
            </div>
        </div>
'''

    # Add JavaScript functionality
    javascript_html = '''
    <script>
        let editMode = false;
        let reportData = {};

        // Toggle edit mode
        function toggleEditMode() {
            editMode = !editMode;
            const editBtn = document.getElementById('editBtn');
            const editIndicator = document.getElementById('editIndicator');
            const editables = document.querySelectorAll('.editable');

            if (editMode) {
                editBtn.innerHTML = '<i class="fas fa-eye"></i> View Mode';
                editBtn.classList.add('active');
                editIndicator.style.display = 'block';

                editables.forEach(el => {
                    el.contentEditable = true;
                    el.classList.add('editing');
                    el.addEventListener('input', saveToLocalStorage);
                });
            } else {
                editBtn.innerHTML = '<i class="fas fa-edit"></i> Edit Mode';
                editBtn.classList.remove('active');
                editIndicator.style.display = 'none';

                editables.forEach(el => {
                    el.contentEditable = false;
                    el.classList.remove('editing');
                    el.removeEventListener('input', saveToLocalStorage);
                });
            }
        }

        // Add comment functionality
        function addComment() {
            const comment = prompt('Enter your comment:');
            if (comment) {
                const commentBox = document.createElement('div');
                commentBox.className = 'comment-box editable';
                commentBox.contentEditable = editMode;
                commentBox.innerHTML = comment;

                // Add to the current section or at the end
                const sections = document.querySelectorAll('.content-section');
                if (sections.length > 0) {
                    const lastSection = sections[sections.length - 1];
                    lastSection.querySelector('.section-content').appendChild(commentBox);
                }
            }
        }

        // Add comment to specific element
        function addCommentToElement(button) {
            const comment = prompt('Enter your comment for this section:');
            if (comment) {
                const commentBox = document.createElement('div');
                commentBox.className = 'comment-box editable';
                commentBox.contentEditable = editMode;
                commentBox.innerHTML = comment;

                button.parentNode.insertBefore(commentBox, button.nextSibling);
            }
        }

        // Add note functionality
        function addNote() {
            const note = prompt('Enter your note:');
            if (note) {
                const noteBox = document.createElement('div');
                noteBox.className = 'note-section';
                noteBox.innerHTML = `
                    <div class="note-title">
                        <i class="fas fa-sticky-note"></i>
                        Custom Note
                    </div>
                    <div class="editable" contenteditable="${editMode}">${note}</div>
                `;

                // Add to the main container
                const mainContainer = document.querySelector('.main-container');
                mainContainer.appendChild(noteBox);
            }
        }

        // Save to localStorage
        function saveToLocalStorage() {
            const editables = document.querySelectorAll('.editable[data-field]');
            const data = {};

            editables.forEach(el => {
                const field = el.getAttribute('data-field');
                data[field] = el.innerHTML;
            });

            localStorage.setItem('reportData', JSON.stringify(data));
            showSaveIndicator();
        }

        // Load from localStorage
        function loadFromLocalStorage() {
            const saved = localStorage.getItem('reportData');
            if (saved) {
                const data = JSON.parse(saved);

                Object.keys(data).forEach(field => {
                    const element = document.querySelector(`[data-field="${field}"]`);
                    if (element) {
                        element.innerHTML = data[field];
                    }
                });
            }
        }

        // Show save indicator
        function showSaveIndicator() {
            const indicator = document.getElementById('saveIndicator');
            indicator.style.display = 'block';
            setTimeout(() => {
                indicator.style.display = 'none';
            }, 2000);
        }

        // Save report
        function saveReport() {
            saveToLocalStorage();

            // Create downloadable version
            const reportContent = document.documentElement.outerHTML;
            const blob = new Blob([reportContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = 'CA_Hospital_RCM_Report_Edited.html';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showSaveIndicator();
        }

        // Export report
        function exportReport() {
            // Create a clean version without edit controls
            const clone = document.cloneNode(true);
            const toolbar = clone.querySelector('.toolbar');
            const editIndicator = clone.querySelector('.edit-indicator');
            const saveIndicator = clone.querySelector('.save-indicator');
            const addCommentBtns = clone.querySelectorAll('.add-comment-btn');

            if (toolbar) toolbar.remove();
            if (editIndicator) editIndicator.remove();
            if (saveIndicator) saveIndicator.remove();
            addCommentBtns.forEach(btn => btn.remove());

            // Remove edit classes and contenteditable
            const editables = clone.querySelectorAll('.editable');
            editables.forEach(el => {
                el.classList.remove('editable', 'editing');
                el.removeAttribute('contenteditable');
                el.removeAttribute('data-field');
            });

            const exportContent = clone.outerHTML;
            const blob = new Blob([exportContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = 'CA_Hospital_RCM_Report_Final.html';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadFromLocalStorage();

            // Auto-save every 30 seconds when in edit mode
            setInterval(() => {
                if (editMode) {
                    saveToLocalStorage();
                }
            }, 30000);
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'e':
                        e.preventDefault();
                        toggleEditMode();
                        break;
                    case 's':
                        e.preventDefault();
                        saveReport();
                        break;
                    case 'm':
                        e.preventDefault();
                        addComment();
                        break;
                }
            }
        });
    </script>
</body>
</html>'''

    # Complete the HTML file
    complete_html = html_start + content_html + javascript_html

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(complete_html)

    print(f"Successfully created editable report: {output_path}")
    print(f"Features: Edit mode, comments, notes, auto-save, export")
    print(f"Keyboard shortcuts: Ctrl+E (edit), Ctrl+S (save), Ctrl+M (comment)")
