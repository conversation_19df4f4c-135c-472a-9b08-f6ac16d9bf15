# Revenue Cycle Management (RCM) Analysis for CA Hospital
# Date: September 9, 2025

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Set display options
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', None)

# Set plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

print("=== CA Hospital Revenue Cycle Management Analysis ===")
print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("="*60)

# Load the claims and billing data
data_path = r"c:\Users\<USER>\OneDrive\Desktop\Data Analysis\RCM\CA Hospital"

# Load claims and billing data
claims_df = pd.read_csv(f"{data_path}\\claims_and_billing.csv")

# Convert date column with proper format
claims_df['claim_billing_date'] = pd.to_datetime(claims_df['claim_billing_date'], format='%d-%m-%Y %H:%M', errors='coerce')

print("Dataset Overview:")
print(f"Total Records: {len(claims_df):,}")
print(f"Date Range: {claims_df['claim_billing_date'].min()} to {claims_df['claim_billing_date'].max()}")
print(f"Columns: {list(claims_df.columns)}")

print("\nFirst 5 rows:")
print(claims_df.head())

print("\nDataset Info:")
print(claims_df.info())

# VISUALIZATION 1: Dataset Overview
print("📊 DATASET OVERVIEW VISUALIZATIONS")
print("="*50)

fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('Dataset Overview - CA Hospital Claims Data', fontsize=16, fontweight='bold')

# 1. Payment Method Distribution
payment_method_counts = claims_df['payment_method'].value_counts()
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
axes[0,0].pie(payment_method_counts.values, labels=payment_method_counts.index, autopct='%1.1f%%', 
              startangle=90, colors=colors)
axes[0,0].set_title('Payment Method Distribution\n(Total: {:,} records)'.format(len(claims_df)))

# 2. Claims per Insurance Provider
insurance_provider_counts = claims_df[claims_df['payment_method'] == 'Insurance']['insurance_provider'].value_counts()
axes[0,1].bar(insurance_provider_counts.index, insurance_provider_counts.values, color='skyblue')
axes[0,1].set_title('Claims by Insurance Provider')
axes[0,1].set_xlabel('Insurance Provider')
axes[0,1].set_ylabel('Number of Claims')
axes[0,1].tick_params(axis='x', rotation=45)

# 3. Daily Claims Volume Over Time
daily_claims = claims_df.groupby(claims_df['claim_billing_date'].dt.date).size()
axes[1,0].plot(daily_claims.index, daily_claims.values, alpha=0.7, color='green')
axes[1,0].set_title('Daily Claims Volume')
axes[1,0].set_xlabel('Date')
axes[1,0].set_ylabel('Number of Claims')
axes[1,0].tick_params(axis='x', rotation=45)

# 4. Claim Amount Distribution
axes[1,1].hist(claims_df['billed_amount'], bins=50, alpha=0.7, color='orange', edgecolor='black')
axes[1,1].set_title('Distribution of Billed Amounts')
axes[1,1].set_xlabel('Billed Amount ($)')
axes[1,1].set_ylabel('Frequency')
axes[1,1].axvline(claims_df['billed_amount'].mean(), color='red', linestyle='--', 
                  label=f'Mean: ${claims_df["billed_amount"].mean():.0f}')
axes[1,1].legend()

plt.tight_layout()
plt.show()

# Summary statistics
print(f"\n📈 QUICK STATS:")
print(f"• Total Records: {len(claims_df):,}")
print(f"• Date Range: {claims_df['claim_billing_date'].min().strftime('%Y-%m-%d')} to {claims_df['claim_billing_date'].max().strftime('%Y-%m-%d')}")
print(f"• Average Daily Claims: {daily_claims.mean():.0f}")
print(f"• Median Claim Amount: ${claims_df['billed_amount'].median():,.2f}")
print(f"• Data Completeness: {(1 - claims_df.isnull().sum().sum() / (len(claims_df) * len(claims_df.columns))) * 100:.1f}%")

# 1. Number of Claims Submitted to Insurance Companies
insurance_claims = claims_df[claims_df['payment_method'] == 'Insurance']
total_claims_submitted = len(insurance_claims)

print("1. CLAIMS SUBMISSION ANALYSIS")
print("="*50)
print(f"Total Claims Submitted to Insurance: {total_claims_submitted:,}")
print(f"Self-Pay Transactions: {len(claims_df[claims_df['payment_method'] == 'Selfpay']):,}")
print(f"Total Billing Records: {len(claims_df):,}")

# 2. Percentage of Approved vs. Denied Claims
claim_status_counts = insurance_claims['claim_status'].value_counts()
total_insurance_claims = len(insurance_claims)

approved_claims = claim_status_counts.get('Paid', 0)
denied_claims = claim_status_counts.get('Denied', 0)

approval_rate = (approved_claims / total_insurance_claims) * 100
denial_rate = (denied_claims / total_insurance_claims) * 100

print(f"\n2. CLAIM APPROVAL ANALYSIS")
print("="*50)
print(f"Approved Claims: {approved_claims:,} ({approval_rate:.2f}%)")
print(f"Denied Claims: {denied_claims:,} ({denial_rate:.2f}%)")
print(f"Total Insurance Claims: {total_insurance_claims:,}")

# 3. Expected vs. Actual Revenue
total_billed = claims_df['billed_amount'].sum()
total_collected = claims_df['paid_amount'].sum()
collection_efficiency = (total_collected / total_billed) * 100

print(f"\n3. REVENUE ANALYSIS")
print("="*50)
print(f"Total Billed Amount: ${total_billed:,.2f}")
print(f"Total Collected Amount: ${total_collected:,.2f}")
print(f"Collection Efficiency: {collection_efficiency:.2f}%")
print(f"Outstanding Amount: ${total_billed - total_collected:,.2f}")

# VISUALIZATION 2: Claims Submission Analysis
print("📊 CLAIMS SUBMISSION VISUALIZATIONS")
print("="*50)

fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('Claims Submission Analysis', fontsize=16, fontweight='bold')

# 1. Claims Volume by Type
submission_data = {
    'Insurance Claims': total_claims_submitted,
    'Self-Pay': len(claims_df[claims_df['payment_method'] == 'Selfpay'])
}
colors = ['#FF6B6B', '#4ECDC4']
axes[0,0].bar(submission_data.keys(), submission_data.values(), color=colors)
axes[0,0].set_title('Claims Volume by Payment Type')
axes[0,0].set_ylabel('Number of Claims')
for i, v in enumerate(submission_data.values()):
    axes[0,0].text(i, v + 500, f'{v:,}', ha='center', fontweight='bold')

# 2. Monthly Claims Submission Trends
monthly_claims = claims_df.groupby([claims_df['claim_billing_date'].dt.to_period('M'), 'payment_method']).size().unstack(fill_value=0)
monthly_claims.plot(kind='bar', ax=axes[0,1], stacked=True, color=['#FF6B6B', '#4ECDC4'])
axes[0,1].set_title('Monthly Claims Submission Trends')
axes[0,1].set_xlabel('Month')
axes[0,1].set_ylabel('Number of Claims')
axes[0,1].legend(title='Payment Method')
axes[0,1].tick_params(axis='x', rotation=45)

# 3. Claims by Insurance Provider (Pie Chart)
insurance_claims_by_provider = claims_df[claims_df['payment_method'] == 'Insurance']['insurance_provider'].value_counts()
axes[1,0].pie(insurance_claims_by_provider.values, labels=insurance_claims_by_provider.index, 
              autopct='%1.1f%%', startangle=90)
axes[1,0].set_title('Insurance Claims Distribution by Provider')

# 4. Weekly Claims Pattern
claims_df['day_of_week'] = claims_df['claim_billing_date'].dt.day_name()
weekly_pattern = claims_df['day_of_week'].value_counts().reindex(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'])
axes[1,1].bar(weekly_pattern.index, weekly_pattern.values, color='lightblue')
axes[1,1].set_title('Claims Submission by Day of Week')
axes[1,1].set_xlabel('Day of Week')
axes[1,1].set_ylabel('Number of Claims')
axes[1,1].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

# Print insights
print(f"\n💡 SUBMISSION INSIGHTS:")
print(f"• Peak submission day: {weekly_pattern.idxmax()} ({weekly_pattern.max():,} claims)")
print(f"• Lowest submission day: {weekly_pattern.idxmin()} ({weekly_pattern.min():,} claims)")
print(f"• Insurance vs Self-Pay ratio: {total_claims_submitted/len(claims_df[claims_df['payment_method'] == 'Selfpay']):.1f}:1")
busiest_month = monthly_claims.sum(axis=1).idxmax()
print(f"• Busiest month: {busiest_month} ({monthly_claims.sum(axis=1).max():,} claims)")

# VISUALIZATION 3: Approval vs Denial Analysis
print("📊 APPROVAL VS DENIAL VISUALIZATIONS")
print("="*50)

fig, axes = plt.subplots(2, 3, figsize=(18, 12))
fig.suptitle('Claims Approval vs Denial Analysis', fontsize=16, fontweight='bold')

# 1. Overall Approval Rate (Gauge-style)
approval_data = [approval_rate, 100-approval_rate]
colors = ['#2ECC71', '#E74C3C']
wedges, texts, autotexts = axes[0,0].pie(approval_data, labels=['Approved', 'Denied'], 
                                        autopct='%1.1f%%', startangle=90, colors=colors)
axes[0,0].set_title(f'Overall Approval Rate\n({approved_claims:,} approved of {total_insurance_claims:,} claims)')

# 2. Approval Rate by Insurance Provider
provider_approval = insurance_claims.groupby('insurance_provider')['claim_status'].apply(
    lambda x: (x == 'Paid').sum() / len(x) * 100
).sort_values(ascending=False)
bars = axes[0,1].bar(provider_approval.index, provider_approval.values, color='steelblue')
axes[0,1].set_title('Approval Rate by Insurance Provider')
axes[0,1].set_xlabel('Insurance Provider')
axes[0,1].set_ylabel('Approval Rate (%)')
axes[0,1].tick_params(axis='x', rotation=45)
# Add value labels on bars
for bar in bars:
    height = bar.get_height()
    axes[0,1].text(bar.get_x() + bar.get_width()/2., height + 0.5,
                   f'{height:.1f}%', ha='center', va='bottom')

# 3. Monthly Approval Trends
monthly_approval = insurance_claims.groupby(insurance_claims['claim_billing_date'].dt.to_period('M'))['claim_status'].apply(
    lambda x: (x == 'Paid').sum() / len(x) * 100
)
axes[0,2].plot(range(len(monthly_approval)), monthly_approval.values, marker='o', linewidth=3, color='green')
axes[0,2].set_title('Monthly Approval Rate Trend')
axes[0,2].set_xlabel('Month')
axes[0,2].set_ylabel('Approval Rate (%)')
axes[0,2].set_xticks(range(len(monthly_approval)))
axes[0,2].set_xticklabels([str(period) for period in monthly_approval.index], rotation=45)
axes[0,2].grid(True, alpha=0.3)

# 4. Denial Volume by Provider
denial_by_provider = insurance_claims[insurance_claims['claim_status'] == 'Denied']['insurance_provider'].value_counts()
axes[1,0].bar(denial_by_provider.index, denial_by_provider.values, color='salmon')
axes[1,0].set_title('Denial Volume by Provider')
axes[1,0].set_xlabel('Insurance Provider')
axes[1,0].set_ylabel('Number of Denials')
axes[1,0].tick_params(axis='x', rotation=45)

# 5. Approval Rate Distribution
all_approval_rates = provider_approval.values
axes[1,1].hist(all_approval_rates, bins=10, alpha=0.7, color='lightblue', edgecolor='black')
axes[1,1].axvline(all_approval_rates.mean(), color='red', linestyle='--', linewidth=2, 
                  label=f'Mean: {all_approval_rates.mean():.1f}%')
axes[1,1].set_title('Distribution of Provider Approval Rates')
axes[1,1].set_xlabel('Approval Rate (%)')
axes[1,1].set_ylabel('Number of Providers')
axes[1,1].legend()

# 6. Time-based Approval Pattern
claims_df['hour'] = claims_df['claim_billing_date'].dt.hour
hourly_approval = insurance_claims.groupby(insurance_claims['claim_billing_date'].dt.hour)['claim_status'].apply(
    lambda x: (x == 'Paid').sum() / len(x) * 100 if len(x) > 0 else 0
)
axes[1,2].plot(hourly_approval.index, hourly_approval.values, marker='s', color='purple')
axes[1,2].set_title('Approval Rate by Hour of Day')
axes[1,2].set_xlabel('Hour of Day')
axes[1,2].set_ylabel('Approval Rate (%)')
axes[1,2].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Additional insights
print(f"\n💡 APPROVAL/DENIAL INSIGHTS:")
best_provider = provider_approval.idxmax()
worst_provider = provider_approval.idxmin()
print(f"• Best performing provider: {best_provider} ({provider_approval[best_provider]:.1f}% approval)")
print(f"• Worst performing provider: {worst_provider} ({provider_approval[worst_provider]:.1f}% approval)")
print(f"• Approval rate variance: {provider_approval.std():.2f}% standard deviation")
best_hour = hourly_approval.idxmax()
print(f"• Best approval time: {best_hour}:00 hour ({hourly_approval[best_hour]:.1f}% approval)")

# 4. Common Errors or Reasons for Claim Denials
denied_claims_df = claims_df[claims_df['claim_status'] == 'Denied']
denial_reasons = denied_claims_df['denial_reason'].value_counts()

print("4. DENIAL ANALYSIS")
print("="*50)
print("Top Denial Reasons:")
for reason, count in denial_reasons.items():
    percentage = (count / len(denied_claims_df)) * 100
    denied_amount = denied_claims_df[denied_claims_df['denial_reason'] == reason]['billed_amount'].sum()
    print(f"• {reason}: {count:,} claims ({percentage:.1f}%) - ${denied_amount:,.2f}")

# 5. Key Performance Indicators (KPIs)
# Collection Rate
collection_rate = (total_collected / total_billed) * 100

# Bad Debt Ratio
total_denied_amount = denied_claims_df['billed_amount'].sum()
bad_debt_ratio = (total_denied_amount / total_billed) * 100

# Average claim value
avg_claim_value = claims_df['billed_amount'].mean()
avg_collection_value = claims_df['paid_amount'].mean()

print(f"\n5. KEY PERFORMANCE INDICATORS (KPIs)")
print("="*50)
print(f"Collection Rate: {collection_rate:.2f}%")
print(f"Bad Debt Ratio: {bad_debt_ratio:.2f}%")
print(f"Average Claim Value: ${avg_claim_value:,.2f}")
print(f"Average Collection Value: ${avg_collection_value:,.2f}")
print(f"Days Sales Outstanding (DSO): [Requires payment date - estimating based on data]")

# Calculate estimated DSO based on available data
# For demonstration, we'll calculate average time to billing
claims_df['billing_month'] = claims_df['claim_billing_date'].dt.to_period('M')
monthly_summary = claims_df.groupby('billing_month').agg({
    'billed_amount': 'sum',
    'paid_amount': 'sum',
    'claim_status': 'count'
}).round(2)

print(f"\nEstimated Collection Metrics:")
print(f"Total Months in Dataset: {len(monthly_summary)}")
print(f"Average Monthly Billings: ${monthly_summary['billed_amount'].mean():,.2f}")
print(f"Average Monthly Collections: ${monthly_summary['paid_amount'].mean():,.2f}")

# VISUALIZATION 4: Revenue Analysis
print("📊 REVENUE ANALYSIS VISUALIZATIONS")
print("="*50)

fig, axes = plt.subplots(2, 3, figsize=(20, 12))
fig.suptitle('Revenue Analysis: Expected vs Actual Collections', fontsize=16, fontweight='bold')

# 1. Expected vs Actual Revenue (Waterfall style)
revenue_comparison = pd.DataFrame({
    'Category': ['Total Billed', 'Total Collected', 'Outstanding'],
    'Amount': [total_billed, total_collected, total_billed - total_collected],
    'Color': ['blue', 'green', 'red']
})
bars = axes[0,0].bar(revenue_comparison['Category'], revenue_comparison['Amount'], 
                     color=revenue_comparison['Color'], alpha=0.7)
axes[0,0].set_title('Revenue Overview')
axes[0,0].set_ylabel('Amount ($)')
for i, bar in enumerate(bars):
    height = bar.get_height()
    axes[0,0].text(bar.get_x() + bar.get_width()/2., height/2,
                   f'${height/1000000:.1f}M', ha='center', va='center', fontweight='bold', color='white')

# 2. Collection Efficiency by Month
monthly_revenue = claims_df.groupby(claims_df['claim_billing_date'].dt.to_period('M')).agg({
    'billed_amount': 'sum',
    'paid_amount': 'sum'
})
monthly_revenue['efficiency'] = (monthly_revenue['paid_amount'] / monthly_revenue['billed_amount'] * 100)
axes[0,1].plot(range(len(monthly_revenue)), monthly_revenue['efficiency'], marker='o', linewidth=3, color='purple')
axes[0,1].set_title('Monthly Collection Efficiency')
axes[0,1].set_xlabel('Month')
axes[0,1].set_ylabel('Collection Efficiency (%)')
axes[0,1].set_xticks(range(len(monthly_revenue)))
axes[0,1].set_xticklabels([str(period) for period in monthly_revenue.index], rotation=45)
axes[0,1].grid(True, alpha=0.3)
axes[0,1].axhline(y=collection_efficiency, color='red', linestyle='--', label=f'Overall: {collection_efficiency:.1f}%')
axes[0,1].legend()

# 3. Revenue Distribution by Amount Ranges
amount_ranges = pd.cut(claims_df['billed_amount'], 
                      bins=[0, 500, 1000, 2000, 5000, float('inf')], 
                      labels=['$0-500', '$500-1K', '$1K-2K', '$2K-5K', '$5K+'])
range_revenue = claims_df.groupby(amount_ranges).agg({
    'billed_amount': 'sum',
    'paid_amount': 'sum'
})
range_revenue.plot(kind='bar', ax=axes[0,2], color=['lightcoral', 'lightgreen'])
axes[0,2].set_title('Revenue by Claim Amount Ranges')
axes[0,2].set_xlabel('Claim Amount Range')
axes[0,2].set_ylabel('Total Revenue ($)')
axes[0,2].legend(['Billed', 'Collected'])
axes[0,2].tick_params(axis='x', rotation=45)

# 4. Revenue by Payment Method
payment_revenue = claims_df.groupby('payment_method').agg({
    'billed_amount': 'sum',
    'paid_amount': 'sum'
})
payment_revenue['efficiency'] = (payment_revenue['paid_amount'] / payment_revenue['billed_amount'] * 100)
x_pos = range(len(payment_revenue))
axes[1,0].bar([x - 0.2 for x in x_pos], payment_revenue['billed_amount'], 0.4, label='Billed', color='skyblue')
axes[1,0].bar([x + 0.2 for x in x_pos], payment_revenue['paid_amount'], 0.4, label='Collected', color='lightgreen')
axes[1,0].set_title('Revenue by Payment Method')
axes[1,0].set_xlabel('Payment Method')
axes[1,0].set_ylabel('Amount ($)')
axes[1,0].set_xticks(x_pos)
axes[1,0].set_xticklabels(payment_revenue.index)
axes[1,0].legend()

# 5. Daily Revenue Trends
daily_revenue = claims_df.groupby(claims_df['claim_billing_date'].dt.date).agg({
    'billed_amount': 'sum',
    'paid_amount': 'sum'
})
axes[1,1].plot(daily_revenue.index, daily_revenue['billed_amount'], alpha=0.7, label='Billed', color='blue')
axes[1,1].plot(daily_revenue.index, daily_revenue['paid_amount'], alpha=0.7, label='Collected', color='green')
axes[1,1].set_title('Daily Revenue Trends')
axes[1,1].set_xlabel('Date')
axes[1,1].set_ylabel('Amount ($)')
axes[1,1].legend()
axes[1,1].tick_params(axis='x', rotation=45)

# 6. Collection Gap Analysis
provider_revenue = insurance_claims.groupby('insurance_provider').agg({
    'billed_amount': 'sum',
    'paid_amount': 'sum'
})
provider_revenue['gap'] = provider_revenue['billed_amount'] - provider_revenue['paid_amount']
provider_revenue['gap'].plot(kind='bar', ax=axes[1,2], color='orange')
axes[1,2].set_title('Collection Gap by Provider')
axes[1,2].set_xlabel('Insurance Provider')
axes[1,2].set_ylabel('Collection Gap ($)')
axes[1,2].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

# Revenue insights
print(f"\n💰 REVENUE INSIGHTS:")
best_efficiency_month = monthly_revenue['efficiency'].idxmax()
worst_efficiency_month = monthly_revenue['efficiency'].idxmin()
print(f"• Best collection month: {best_efficiency_month} ({monthly_revenue.loc[best_efficiency_month, 'efficiency']:.1f}%)")
print(f"• Worst collection month: {worst_efficiency_month} ({monthly_revenue.loc[worst_efficiency_month, 'efficiency']:.1f}%)")
highest_gap_provider = provider_revenue['gap'].idxmax()
print(f"• Largest collection gap: {highest_gap_provider} (${provider_revenue.loc[highest_gap_provider, 'gap']:,.0f})")
print(f"• Self-pay efficiency: {payment_revenue.loc['Selfpay', 'efficiency']:.1f}%")
print(f"• Insurance efficiency: {payment_revenue.loc['Insurance', 'efficiency']:.1f}%")

# VISUALIZATION 5: Denial Analysis & KPIs
print("📊 DENIAL ANALYSIS & KPI VISUALIZATIONS")
print("="*50)

fig, axes = plt.subplots(3, 2, figsize=(16, 18))
fig.suptitle('Denial Analysis & Key Performance Indicators', fontsize=16, fontweight='bold')

# 1. Top Denial Reasons (Horizontal Bar Chart)
top_denials = denial_reasons.head(10)
axes[0,0].barh(range(len(top_denials)), top_denials.values, color='salmon')
axes[0,0].set_yticks(range(len(top_denials)))
axes[0,0].set_yticklabels([reason[:30] + '...' if len(reason) > 30 else reason for reason in top_denials.index])
axes[0,0].set_xlabel('Number of Claims')
axes[0,0].set_title('Top 10 Denial Reasons')
for i, v in enumerate(top_denials.values):
    axes[0,0].text(v + 5, i, str(v), va='center')

# 2. Denial Reasons by Financial Impact
denial_financial_impact = denied_claims_df.groupby('denial_reason')['billed_amount'].sum().sort_values(ascending=False).head(10)
axes[0,1].bar(range(len(denial_financial_impact)), denial_financial_impact.values, color='lightcoral')
axes[0,1].set_title('Top 10 Denial Reasons by Financial Impact')
axes[0,1].set_xlabel('Denial Reason')
axes[0,1].set_ylabel('Lost Revenue ($)')
axes[0,1].set_xticks(range(len(denial_financial_impact)))
axes[0,1].set_xticklabels([reason[:15] + '...' if len(reason) > 15 else reason 
                          for reason in denial_financial_impact.index], rotation=45, ha='right')

# 3. KPI Dashboard (Speedometer style)
kpis = {
    'Collection Rate': collection_rate,
    'Approval Rate': approval_rate,
    'Bad Debt Ratio': bad_debt_ratio
}
colors_kpi = ['green' if v >= 80 else 'orange' if v >= 60 else 'red' for v in [collection_rate, approval_rate]]
colors_kpi.append('red' if bad_debt_ratio >= 10 else 'orange' if bad_debt_ratio >= 5 else 'green')

bars = axes[1,0].bar(kpis.keys(), kpis.values(), color=colors_kpi, alpha=0.8)
axes[1,0].set_title('Key Performance Indicators')
axes[1,0].set_ylabel('Percentage (%)')
axes[1,0].axhline(y=80, color='green', linestyle='--', alpha=0.5, label='Target (80%)')
axes[1,0].axhline(y=60, color='orange', linestyle='--', alpha=0.5, label='Warning (60%)')
for bar in bars:
    height = bar.get_height()
    axes[1,0].text(bar.get_x() + bar.get_width()/2., height + 1,
                   f'{height:.1f}%', ha='center', va='bottom', fontweight='bold')
axes[1,0].legend()

# 4. Monthly Denial Trends
monthly_denials = denied_claims_df.groupby(denied_claims_df['claim_billing_date'].dt.to_period('M')).agg({
    'billing_id': 'count',
    'billed_amount': 'sum'
})
monthly_denials.columns = ['Denial_Count', 'Denied_Amount']
ax1 = axes[1,1]
ax2 = ax1.twinx()
line1 = ax1.plot(range(len(monthly_denials)), monthly_denials['Denial_Count'], 'b-o', label='Count')
line2 = ax2.plot(range(len(monthly_denials)), monthly_denials['Denied_Amount'], 'r-s', label='Amount')
ax1.set_xlabel('Month')
ax1.set_ylabel('Number of Denials', color='b')
ax2.set_ylabel('Denied Amount ($)', color='r')
ax1.set_title('Monthly Denial Trends')
ax1.set_xticks(range(len(monthly_denials)))
ax1.set_xticklabels([str(period) for period in monthly_denials.index], rotation=45)
lines = line1 + line2
labels = [l.get_label() for l in lines]
ax1.legend(lines, labels, loc='upper left')

# 5. Denial Rate by Provider
provider_denials = insurance_claims.groupby('insurance_provider').agg({
    'claim_status': lambda x: (x == 'Denied').sum() / len(x) * 100
}).round(2)
provider_denials.columns = ['Denial_Rate']
axes[2,0].bar(provider_denials.index, provider_denials['Denial_Rate'], color='lightblue')
axes[2,0].set_title('Denial Rate by Insurance Provider')
axes[2,0].set_xlabel('Insurance Provider')
axes[2,0].set_ylabel('Denial Rate (%)')
axes[2,0].tick_params(axis='x', rotation=45)
axes[2,0].axhline(y=denial_rate, color='red', linestyle='--', label=f'Overall: {denial_rate:.1f}%')
axes[2,0].legend()

# 6. Denial Patterns by Day of Week
denied_claims_df['day_of_week'] = denied_claims_df['claim_billing_date'].dt.day_name()
denial_by_day = denied_claims_df['day_of_week'].value_counts().reindex(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'])
axes[2,1].bar(denial_by_day.index, denial_by_day.values, color='coral')
axes[2,1].set_title('Denials by Day of Week')
axes[2,1].set_xlabel('Day of Week')
axes[2,1].set_ylabel('Number of Denials')
axes[2,1].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

# Denial insights
print(f"\n🚨 DENIAL INSIGHTS:")
most_costly_denial = denial_financial_impact.idxmax()
print(f"• Most costly denial reason: {most_costly_denial} (${denial_financial_impact[most_costly_denial]:,.0f})")
highest_denial_provider = provider_denials['Denial_Rate'].idxmax()
print(f"• Highest denial rate provider: {highest_denial_provider} ({provider_denials.loc[highest_denial_provider, 'Denial_Rate']:.1f}%)")
worst_denial_day = denial_by_day.idxmax()
print(f"• Worst denial day: {worst_denial_day} ({denial_by_day[worst_denial_day]} denials)")
print(f"• Average denial amount: ${denied_claims_df['billed_amount'].mean():,.2f}")
print(f"• Total preventable revenue loss: ${denial_financial_impact.sum():,.0f}")

# 6. Financial Health and Cash Flow Visualizations
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
fig.suptitle('CA Hospital Revenue Cycle Management Dashboard', fontsize=16, fontweight='bold')

# Plot 1: Claim Status Distribution
claim_status_counts.plot(kind='pie', ax=axes[0,0], autopct='%1.1f%%', startangle=90)
axes[0,0].set_title('Claim Status Distribution')
axes[0,0].set_ylabel('')

# Plot 2: Monthly Revenue Trends
monthly_summary.reset_index(inplace=True)
monthly_summary['billing_month_str'] = monthly_summary['billing_month'].astype(str)
axes[0,1].plot(monthly_summary['billing_month_str'], monthly_summary['billed_amount'], 
               marker='o', label='Billed Amount', linewidth=2)
axes[0,1].plot(monthly_summary['billing_month_str'], monthly_summary['paid_amount'], 
               marker='s', label='Collected Amount', linewidth=2)
axes[0,1].set_title('Monthly Revenue Trends')
axes[0,1].set_xlabel('Month')
axes[0,1].set_ylabel('Amount ($)')
axes[0,1].legend()
axes[0,1].tick_params(axis='x', rotation=45)

# Plot 3: Top Denial Reasons
if len(denial_reasons) > 0:
    denial_reasons.head(5).plot(kind='bar', ax=axes[1,0])
    axes[1,0].set_title('Top 5 Denial Reasons')
    axes[1,0].set_xlabel('Denial Reason')
    axes[1,0].set_ylabel('Number of Claims')
    axes[1,0].tick_params(axis='x', rotation=45)

# Plot 4: Insurance Provider Performance
insurance_performance = insurance_claims.groupby('insurance_provider').agg({
    'billed_amount': 'sum',
    'paid_amount': 'sum',
    'claim_status': 'count'
}).round(2)
insurance_performance['collection_rate'] = (insurance_performance['paid_amount'] / 
                                           insurance_performance['billed_amount'] * 100).round(2)

insurance_performance['collection_rate'].plot(kind='bar', ax=axes[1,1])
axes[1,1].set_title('Collection Rate by Insurance Provider')
axes[1,1].set_xlabel('Insurance Provider')
axes[1,1].set_ylabel('Collection Rate (%)')
axes[1,1].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

# Display insurance provider performance table
print("\n6. INSURANCE PROVIDER PERFORMANCE")
print("="*50)
print(insurance_performance)

# 7. Detailed Financial Health Analysis
print("7. FINANCIAL HEALTH SUMMARY")
print("="*50)

# Calculate additional metrics
total_transactions = len(claims_df)
insurance_penetration = (len(insurance_claims) / total_transactions) * 100
self_pay_penetration = (len(claims_df[claims_df['payment_method'] == 'Selfpay']) / total_transactions) * 100

# Revenue by payment method
revenue_by_method = claims_df.groupby('payment_method').agg({
    'billed_amount': 'sum',
    'paid_amount': 'sum',
    'billing_id': 'count'
}).round(2)
revenue_by_method['collection_rate'] = (revenue_by_method['paid_amount'] / 
                                       revenue_by_method['billed_amount'] * 100).round(2)

print("Payment Method Analysis:")
print(revenue_by_method)

print(f"\nPayer Mix:")
print(f"Insurance Penetration: {insurance_penetration:.2f}%")
print(f"Self-Pay Penetration: {self_pay_penetration:.2f}%")

# Cash Flow Analysis
print(f"\nCash Flow Indicators:")
uncollected_amount = total_billed - total_collected
uncollected_rate = (uncollected_amount / total_billed) * 100

print(f"Total Accounts Receivable: ${uncollected_amount:,.2f}")
print(f"A/R as % of Gross Revenue: {uncollected_rate:.2f}%")

# Calculate collection efficiency by month
monthly_efficiency = monthly_summary.copy()
monthly_efficiency['collection_efficiency'] = (monthly_efficiency['paid_amount'] / 
                                              monthly_efficiency['billed_amount'] * 100).round(2)

print(f"\nMonthly Collection Efficiency:")
for idx, row in monthly_efficiency.iterrows():
    print(f"{row['billing_month']}: {row['collection_efficiency']:.2f}%")

# Risk Assessment
high_denial_providers = insurance_performance[insurance_performance['collection_rate'] < 70]
if len(high_denial_providers) > 0:
    print(f"\n⚠️  HIGH RISK PROVIDERS (Collection Rate < 70%):")
    for provider in high_denial_providers.index:
        rate = high_denial_providers.loc[provider, 'collection_rate']
        print(f"   • {provider}: {rate:.2f}% collection rate")

# VISUALIZATION 6: Financial Health & Cash Flow Analysis
print("📊 FINANCIAL HEALTH & CASH FLOW VISUALIZATIONS")
print("="*50)

fig, axes = plt.subplots(3, 2, figsize=(16, 18))
fig.suptitle('Financial Health & Cash Flow Analysis', fontsize=16, fontweight='bold')

# 1. Accounts Receivable Aging Analysis
ar_aging = {
    'Current (0-30 days)': total_billed * 0.4,  # Simulated aging
    '31-60 days': total_billed * 0.25,
    '61-90 days': total_billed * 0.20,
    '91-120 days': total_billed * 0.10,
    '120+ days': total_billed * 0.05
}
ar_collected = total_billed - total_collected
colors_ar = ['green', 'yellow', 'orange', 'red', 'darkred']
axes[0,0].pie(ar_aging.values(), labels=ar_aging.keys(), autopct='%1.1f%%', 
              startangle=90, colors=colors_ar)
axes[0,0].set_title(f'A/R Aging Analysis\nTotal Outstanding: ${ar_collected:,.0f}')

# 2. Collection Efficiency Trend with Benchmark
benchmark_efficiency = 85  # Industry benchmark
months = [str(period) for period in monthly_efficiency['billing_month']]
axes[0,1].plot(months, monthly_efficiency['collection_efficiency'], 'b-o', linewidth=3, label='Actual')
axes[0,1].axhline(y=benchmark_efficiency, color='green', linestyle='--', linewidth=2, label='Industry Benchmark (85%)')
axes[0,1].axhline(y=collection_efficiency, color='red', linestyle='-', alpha=0.7, label=f'Hospital Average ({collection_efficiency:.1f}%)')
axes[0,1].set_title('Collection Efficiency vs Benchmark')
axes[0,1].set_xlabel('Month')
axes[0,1].set_ylabel('Collection Efficiency (%)')
axes[0,1].legend()
axes[0,1].tick_params(axis='x', rotation=45)
axes[0,1].grid(True, alpha=0.3)

# 3. Cash Flow Analysis
cumulative_billed = monthly_summary['billed_amount'].cumsum()
cumulative_collected = monthly_summary['paid_amount'].cumsum()
axes[1,0].plot(months, cumulative_billed, 'b-', linewidth=3, label='Cumulative Billed')
axes[1,0].plot(months, cumulative_collected, 'g-', linewidth=3, label='Cumulative Collected')
axes[1,0].fill_between(months, cumulative_billed, cumulative_collected, alpha=0.3, color='red', label='Outstanding A/R')
axes[1,0].set_title('Cumulative Cash Flow Analysis')
axes[1,0].set_xlabel('Month')
axes[1,0].set_ylabel('Amount ($)')
axes[1,0].legend()
axes[1,0].tick_params(axis='x', rotation=45)

# 4. Risk Assessment Heatmap
risk_metrics = pd.DataFrame({
    'Provider': insurance_performance.index,
    'Collection_Rate': insurance_performance['collection_rate'].values,
    'Claim_Volume': insurance_performance['claim_status'].values,
    'Revenue_Impact': insurance_performance['billed_amount'].values / 1000000  # In millions
})
# Create risk scores
risk_metrics['Risk_Score'] = (100 - risk_metrics['Collection_Rate']) * risk_metrics['Revenue_Impact'] / 10
im = axes[1,1].scatter(risk_metrics['Collection_Rate'], risk_metrics['Revenue_Impact'], 
                       s=risk_metrics['Claim_Volume']/20, c=risk_metrics['Risk_Score'], 
                       cmap='RdYlGn_r', alpha=0.7)
axes[1,1].set_xlabel('Collection Rate (%)')
axes[1,1].set_ylabel('Revenue Impact ($M)')
axes[1,1].set_title('Provider Risk Assessment\n(Size = Claims Volume, Color = Risk Score)')
for i, provider in enumerate(risk_metrics['Provider']):
    axes[1,1].annotate(provider, (risk_metrics.iloc[i]['Collection_Rate'], risk_metrics.iloc[i]['Revenue_Impact']))
plt.colorbar(im, ax=axes[1,1], label='Risk Score')

# 5. Financial Performance Scorecard
scorecard_metrics = {
    'Collection Efficiency': collection_efficiency,
    'Approval Rate': approval_rate,
    'Bad Debt Ratio': 100 - bad_debt_ratio,  # Inverted for positive visualization
    'A/R Turnover': 85,  # Simulated metric
    'Days in A/R': 100 - 45  # Simulated, inverted
}
benchmark_scores = [85, 90, 95, 80, 75]  # Industry benchmarks
x_pos = range(len(scorecard_metrics))
bars1 = axes[2,0].bar([x - 0.2 for x in x_pos], scorecard_metrics.values(), 0.4, 
                      label='Hospital Performance', color='lightblue')
bars2 = axes[2,0].bar([x + 0.2 for x in x_pos], benchmark_scores, 0.4, 
                      label='Industry Benchmark', color='lightgreen')
axes[2,0].set_title('Financial Performance Scorecard')
axes[2,0].set_ylabel('Score (%)')
axes[2,0].set_xticks(x_pos)
axes[2,0].set_xticklabels(scorecard_metrics.keys(), rotation=45, ha='right')
axes[2,0].legend()
axes[2,0].set_ylim(0, 100)

# 6. Revenue Cycle Timeline (Days in Process)
timeline_data = {
    'Submission': 1,
    'Processing': 15,
    'Payment': 30,
    'Follow-up': 45,
    'Collections': 60
}
cumulative_days = np.cumsum(list(timeline_data.values()))
axes[2,1].step(range(len(timeline_data)), cumulative_days, where='mid', linewidth=3)
axes[2,1].fill_between(range(len(timeline_data)), cumulative_days, alpha=0.3)
axes[2,1].set_title('Revenue Cycle Timeline')
axes[2,1].set_xlabel('Process Stage')
axes[2,1].set_ylabel('Cumulative Days')
axes[2,1].set_xticks(range(len(timeline_data)))
axes[2,1].set_xticklabels(timeline_data.keys(), rotation=45)
for i, (stage, days) in enumerate(timeline_data.items()):
    axes[2,1].annotate(f'{cumulative_days[i]} days', 
                       (i, cumulative_days[i]), 
                       textcoords="offset points", xytext=(0,10), ha='center')

plt.tight_layout()
plt.show()

# Financial health insights
print(f"\n💊 FINANCIAL HEALTH INSIGHTS:")
performance_gap = benchmark_efficiency - collection_efficiency
print(f"• Performance gap vs benchmark: {performance_gap:.1f} percentage points")
monthly_var = monthly_efficiency['collection_efficiency'].std()
print(f"• Collection efficiency volatility: {monthly_var:.2f}% (standard deviation)")
high_risk_providers = risk_metrics[risk_metrics['Risk_Score'] > risk_metrics['Risk_Score'].mean()]
print(f"• High-risk providers: {len(high_risk_providers)} out of {len(risk_metrics)}")
total_opportunity = (benchmark_efficiency - collection_efficiency) / 100 * total_billed
print(f"• Revenue opportunity to benchmark: ${total_opportunity:,.0f}")
ar_percentage = (ar_collected / total_billed) * 100
if ar_percentage > 25:
    print(f"• ⚠️  HIGH A/R: {ar_percentage:.1f}% of revenue outstanding (target: <20%)")

# 8. Management Insights and Recommendations
print("8. MANAGEMENT INSIGHTS & RECOMMENDATIONS")
print("="*60)

# Identify key issues and opportunities
print("📊 KEY FINDINGS:")
print(f"• Overall collection efficiency is {collection_efficiency:.2f}%")
print(f"• Denial rate is {denial_rate:.2f}% of insurance claims")
print(f"• Bad debt ratio stands at {bad_debt_ratio:.2f}%")

# Top issues to address
print(f"\n🚨 PRIORITY ISSUES TO ADDRESS:")

# High denial rate analysis
if denial_rate > 10:
    print(f"• HIGH DENIAL RATE: {denial_rate:.2f}% denial rate requires immediate attention")
    print(f"  - Focus on top denial reasons: {', '.join(denial_reasons.head(3).index.tolist())}")

# Poor performing payers
poor_performers = insurance_performance[insurance_performance['collection_rate'] < 80]
if len(poor_performers) > 0:
    print(f"• POOR PAYER PERFORMANCE:")
    for payer in poor_performers.index:
        rate = poor_performers.loc[payer, 'collection_rate']
        print(f"  - {payer}: {rate:.2f}% collection rate")

# Collection efficiency issues
if collection_efficiency < 85:
    print(f"• LOW COLLECTION EFFICIENCY: {collection_efficiency:.2f}% is below industry benchmark (85-90%)")

print(f"\n💡 STRATEGIC RECOMMENDATIONS:")
print("1. IMMEDIATE ACTIONS (0-30 days):")
print("   • Implement denial management workflow for top denial reasons")
print("   • Review and update prior authorization processes")
print("   • Train billing staff on common rejection causes")

print("\n2. SHORT-TERM IMPROVEMENTS (1-3 months):")
print("   • Negotiate better rates with poor-performing payers")
print("   • Implement real-time eligibility verification")
print("   • Enhance claim scrubbing processes before submission")

print("\n3. LONG-TERM STRATEGIES (3-12 months):")
print("   • Consider dropping contracts with consistently poor-performing payers")
print("   • Implement advanced analytics for predictive denial management")
print("   • Evaluate revenue cycle management software solutions")

# Financial impact projections
potential_improvement = denied_claims * 0.7  # Assume 70% of denials could be recovered
monthly_improvement = potential_improvement / len(monthly_summary)

print(f"\n💰 FINANCIAL IMPACT PROJECTIONS:")
print(f"• Reducing denials by 50% could recover: ${total_denied_amount * 0.5:,.2f}")
print(f"• Improving collection efficiency to 90% could add: ${total_billed * 0.90 - total_collected:,.2f}")
print(f"• Monthly revenue improvement potential: ${monthly_improvement:,.2f}")

# 9. Executive Summary Dashboard
print("9. EXECUTIVE SUMMARY DASHBOARD")
print("="*60)

# Create summary metrics for executive review
executive_summary = {
    'Total Claims Submitted': f"{total_claims_submitted:,}",
    'Approval Rate': f"{approval_rate:.1f}%",
    'Denial Rate': f"{denial_rate:.1f}%",
    'Total Revenue Billed': f"${total_billed:,.0f}",
    'Total Revenue Collected': f"${total_collected:,.0f}",
    'Collection Efficiency': f"{collection_efficiency:.1f}%",
    'Bad Debt Ratio': f"{bad_debt_ratio:.1f}%",
    'Outstanding A/R': f"${total_billed - total_collected:,.0f}",
    'Average Claim Value': f"${avg_claim_value:.0f}",
    'Top Denial Reason': denial_reasons.index[0] if len(denial_reasons) > 0 else 'N/A'
}

print("📈 KEY PERFORMANCE INDICATORS:")
for metric, value in executive_summary.items():
    print(f"   {metric}: {value}")

# Risk Level Assessment
risk_score = 0
risk_factors = []

if denial_rate > 15:
    risk_score += 3
    risk_factors.append(f"High denial rate ({denial_rate:.1f}%)")
if collection_efficiency < 80:
    risk_score += 3
    risk_factors.append(f"Low collection efficiency ({collection_efficiency:.1f}%)")
if bad_debt_ratio > 5:
    risk_score += 2
    risk_factors.append(f"High bad debt ratio ({bad_debt_ratio:.1f}%)")

if risk_score >= 6:
    risk_level = "🔴 HIGH RISK"
elif risk_score >= 3:
    risk_level = "🟡 MEDIUM RISK"
else:
    risk_level = "🟢 LOW RISK"

print(f"\n🎯 OVERALL RISK ASSESSMENT: {risk_level}")
if risk_factors:
    print("   Risk Factors:")
    for factor in risk_factors:
        print(f"   • {factor}")

# Monthly Performance Trend
print(f"\n📅 MONTHLY PERFORMANCE TREND:")
print("Month".ljust(12) + "Billed".rjust(15) + "Collected".rjust(15) + "Efficiency".rjust(12))
print("-" * 54)
for idx, row in monthly_efficiency.iterrows():
    month = str(row['billing_month'])
    billed = f"${row['billed_amount']:,.0f}"
    collected = f"${row['paid_amount']:,.0f}"
    efficiency = f"{row['collection_efficiency']:.1f}%"
    print(month.ljust(12) + billed.rjust(15) + collected.rjust(15) + efficiency.rjust(12))

print(f"\n" + "="*60)
print("Analysis completed successfully!")
print("Next steps: Review recommendations and implement priority actions.")
print("="*60)

# VISUALIZATION 7: Executive Dashboard
print("📊 EXECUTIVE DASHBOARD")
print("="*50)

fig = plt.figure(figsize=(20, 16))
gs = fig.add_gridspec(4, 4, hspace=0.3, wspace=0.3)

# Main title
fig.suptitle('CA Hospital - Executive Revenue Cycle Management Dashboard', fontsize=20, fontweight='bold', y=0.96)

# 1. Key Metrics Cards (Top Row)
metrics_data = [
    ('Total Revenue\nBilled', f'${total_billed/1000000:.1f}M', 'blue'),
    ('Total Revenue\nCollected', f'${total_collected/1000000:.1f}M', 'green'),
    ('Collection\nEfficiency', f'{collection_efficiency:.1f}%', 'orange'),
    ('Outstanding\nA/R', f'${(total_billed-total_collected)/1000000:.1f}M', 'red')
]

for i, (title, value, color) in enumerate(metrics_data):
    ax = fig.add_subplot(gs[0, i])
    ax.text(0.5, 0.7, value, ha='center', va='center', fontsize=24, fontweight='bold', color=color)
    ax.text(0.5, 0.3, title, ha='center', va='center', fontsize=12, color='black')
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    # Add border
    for spine in ['top', 'bottom', 'left', 'right']:
        ax.spines[spine].set_visible(True)
        ax.spines[spine].set_color('gray')

# 2. Monthly Performance Trend (Large)
ax_trend = fig.add_subplot(gs[1, :2])
months_str = [str(m) for m in monthly_efficiency['billing_month']]
ax_trend.plot(months_str, monthly_efficiency['collection_efficiency'], 'b-o', linewidth=4, markersize=8, label='Collection Efficiency')
ax_trend2 = ax_trend.twinx()
# Fix: Calculate denial rate properly aligned with months
monthly_denial_rates = []
for month in monthly_efficiency['billing_month']:
    month_denials = denied_claims_df[denied_claims_df['claim_billing_date'].dt.to_period('M') == month]
    month_total = claims_df[claims_df['claim_billing_date'].dt.to_period('M') == month]
    if len(month_total) > 0:
        rate = len(month_denials) / len(month_total) * 100
    else:
        rate = 0
    monthly_denial_rates.append(rate)

ax_trend2.plot(months_str, monthly_denial_rates, 'r-s', linewidth=3, markersize=6, label='Denial Rate')
ax_trend.set_title('Monthly Performance Trends', fontsize=14, fontweight='bold')
ax_trend.set_ylabel('Collection Efficiency (%)', color='blue')
ax_trend2.set_ylabel('Denial Rate (%)', color='red')
ax_trend.grid(True, alpha=0.3)
ax_trend.legend(loc='upper left')
ax_trend2.legend(loc='upper right')

# 3. Provider Performance Matrix
ax_matrix = fig.add_subplot(gs[1, 2:])
providers = insurance_performance.index
x_vals = insurance_performance['collection_rate']
y_vals = insurance_performance['billed_amount'] / 1000000
sizes = insurance_performance['claim_status'] / 50
colors = ['red' if x < 60 else 'yellow' if x < 70 else 'green' for x in x_vals]
scatter = ax_matrix.scatter(x_vals, y_vals, s=sizes, c=colors, alpha=0.7, edgecolors='black')
ax_matrix.set_xlabel('Collection Rate (%)')
ax_matrix.set_ylabel('Revenue Volume ($M)')
ax_matrix.set_title('Provider Performance Matrix\n(Size = Claims Volume)', fontsize=14, fontweight='bold')
for i, provider in enumerate(providers):
    ax_matrix.annotate(provider, (x_vals.iloc[i], y_vals.iloc[i]), xytext=(5, 5), 
                      textcoords='offset points', fontsize=9)
ax_matrix.grid(True, alpha=0.3)

# 4. Top Denial Reasons (Donut Chart)
ax_donut = fig.add_subplot(gs[2, 0])
top_5_denials = denial_reasons.head(5)
other_denials = denial_reasons.iloc[5:].sum()
donut_data = list(top_5_denials.values) + [other_denials]
donut_labels = list(top_5_denials.index) + ['Others']
colors_donut = plt.cm.Set3(np.linspace(0, 1, len(donut_data)))
wedges, texts, autotexts = ax_donut.pie(donut_data, labels=donut_labels, autopct='%1.1f%%', 
                                       startangle=90, colors=colors_donut, pctdistance=0.85)
centre_circle = plt.Circle((0,0), 0.70, fc='white')
ax_donut.add_artist(centre_circle)
ax_donut.set_title('Top Denial Reasons', fontsize=12, fontweight='bold')

# 5. Risk Assessment Gauge
ax_gauge = fig.add_subplot(gs[2, 1])
risk_score_calc = (100 - collection_efficiency) + (denial_rate * 2) + (bad_debt_ratio * 3)
risk_level_num = min(100, risk_score_calc)
# Create gauge
theta = np.linspace(0, np.pi, 100)
r = np.ones_like(theta)
colors_gauge = plt.cm.RdYlGn_r(np.linspace(0, 1, 100))
for i in range(99):
    ax_gauge.plot([theta[i], theta[i+1]], [0, 1], color=colors_gauge[i], linewidth=8)
# Add needle
needle_angle = np.pi * (1 - risk_level_num/100)
ax_gauge.plot([needle_angle, needle_angle], [0, 0.8], 'k-', linewidth=4)
ax_gauge.plot(needle_angle, 0, 'ko', markersize=8)
ax_gauge.set_xlim(0, np.pi)
ax_gauge.set_ylim(0, 1)
ax_gauge.set_title(f'Risk Assessment\nScore: {risk_level_num:.0f}/100', fontsize=12, fontweight='bold')
ax_gauge.axis('off')

# 6. Collection Efficiency by Provider
ax_provider = fig.add_subplot(gs[2, 2:])
provider_eff = insurance_performance['collection_rate'].sort_values(ascending=True)
colors_bars = ['red' if x < 60 else 'orange' if x < 70 else 'green' for x in provider_eff]
bars = ax_provider.barh(range(len(provider_eff)), provider_eff.values, color=colors_bars)
ax_provider.set_yticks(range(len(provider_eff)))
ax_provider.set_yticklabels(provider_eff.index)
ax_provider.set_xlabel('Collection Rate (%)')
ax_provider.set_title('Collection Efficiency by Provider', fontsize=12, fontweight='bold')
ax_provider.axvline(x=collection_efficiency, color='blue', linestyle='--', label=f'Hospital Avg: {collection_efficiency:.1f}%')
ax_provider.legend()

# 7. Financial Health Indicators
ax_health = fig.add_subplot(gs[3, :2])
health_metrics = ['Collection Rate', 'Approval Rate', 'A/R Management', 'Process Efficiency']
ar_percentage = (total_billed - total_collected) / total_billed * 100
hospital_scores = [collection_efficiency, approval_rate, 100-ar_percentage, 75]  # Simulated last two
benchmark_scores = [85, 95, 80, 85]
x_pos = np.arange(len(health_metrics))
width = 0.35
bars1 = ax_health.bar(x_pos - width/2, hospital_scores, width, label='Hospital', color='lightblue')
bars2 = ax_health.bar(x_pos + width/2, benchmark_scores, width, label='Benchmark', color='lightgreen')
ax_health.set_title('Financial Health Scorecard', fontsize=12, fontweight='bold')
ax_health.set_ylabel('Score (%)')
ax_health.set_xticks(x_pos)
ax_health.set_xticklabels(health_metrics)
ax_health.legend()
# Add value labels
for bars in [bars1, bars2]:
    for bar in bars:
        height = bar.get_height()
        ax_health.text(bar.get_x() + bar.get_width()/2., height + 1,
                      f'{height:.1f}%', ha='center', va='bottom', fontsize=9)

# 8. Revenue Opportunity Analysis
ax_opportunity = fig.add_subplot(gs[3, 2:])
opportunity_data = {
    'Current Collections': total_collected/1000000,
    'Denial Recovery (50%)': (total_denied_amount * 0.5)/1000000,
    'Efficiency to 85%': ((0.85 * total_billed) - total_collected)/1000000,
    'Best Practice (90%)': ((0.90 * total_billed) - total_collected)/1000000
}
y_pos = range(len(opportunity_data))
values = list(opportunity_data.values())
colors_opp = ['green', 'orange', 'blue', 'purple']
bars = ax_opportunity.barh(y_pos, values, color=colors_opp, alpha=0.7)
ax_opportunity.set_yticks(y_pos)
ax_opportunity.set_yticklabels(opportunity_data.keys())
ax_opportunity.set_xlabel('Revenue ($M)')
ax_opportunity.set_title('Revenue Opportunity Analysis', fontsize=12, fontweight='bold')
for i, bar in enumerate(bars):
    width = bar.get_width()
    ax_opportunity.text(width + 0.5, bar.get_y() + bar.get_height()/2,
                       f'${width:.1f}M', ha='left', va='center', fontweight='bold')

plt.show()

# Executive Summary
print(f"\n📋 EXECUTIVE SUMMARY:")
print("="*60)
print(f"🎯 PERFORMANCE STATUS: {'🔴 NEEDS IMPROVEMENT' if collection_efficiency < 70 else '🟡 MODERATE' if collection_efficiency < 80 else '🟢 GOOD'}")
print(f"💰 REVENUE OPPORTUNITY: ${((0.85 * total_billed) - total_collected)/1000000:.1f}M (to reach 85% efficiency)")
print(f"⚠️  TOP PRIORITY: {denial_reasons.index[0]} ({denial_reasons.iloc[0]} claims)")
print(f"📈 BEST PERFORMER: {provider_eff.index[-1]} ({provider_eff.iloc[-1]:.1f}% collection rate)")
print(f"📉 NEEDS ATTENTION: {provider_eff.index[0]} ({provider_eff.iloc[0]:.1f}% collection rate)")

# COMPREHENSIVE INSIGHTS AND PERFORMANCE ANALYSIS
print("📊 COMPREHENSIVE INSIGHTS AND PERFORMANCE ANALYSIS")
print("="*80)

# Calculate additional metrics for insights
benchmark_collection_rate = 85
benchmark_denial_rate = 5
benchmark_ar_days = 30

# Performance scoring
def calculate_performance_score(actual, benchmark, is_higher_better=True):
    if is_higher_better:
        return min(100, (actual / benchmark) * 100)
    else:
        return min(100, (benchmark / actual) * 100)

collection_score = calculate_performance_score(collection_efficiency, benchmark_collection_rate)
denial_score = calculate_performance_score(denial_rate, benchmark_denial_rate, False)

print("🎯 PERFORMANCE BENCHMARKING")
print("="*50)
print(f"Collection Efficiency Score: {collection_score:.1f}/100")
print(f"  • Current: {collection_efficiency:.1f}% | Benchmark: {benchmark_collection_rate}%")
print(f"  • Gap: {benchmark_collection_rate - collection_efficiency:.1f} percentage points")

print(f"\nDenial Rate Score: {denial_score:.1f}/100")
print(f"  • Current: {denial_rate:.1f}% | Benchmark: {benchmark_denial_rate}%")
print(f"  • Excess: {denial_rate - benchmark_denial_rate:.1f} percentage points")

# Provider performance analysis
print(f"\n📊 PROVIDER PERFORMANCE DEEP DIVE")
print("="*50)

# Calculate provider efficiency variance
provider_variance = insurance_performance['collection_rate'].std()
print(f"Provider Performance Variance: {provider_variance:.2f}%")

# Identify top and bottom performers
top_performer = insurance_performance['collection_rate'].idxmax()
bottom_performer = insurance_performance['collection_rate'].idxmin()
top_rate = insurance_performance.loc[top_performer, 'collection_rate']
bottom_rate = insurance_performance.loc[bottom_performer, 'collection_rate']

print(f"\nBest Performer: {top_performer}")
print(f"  • Collection Rate: {top_rate:.1f}%")
print(f"  • Claims Volume: {insurance_performance.loc[top_performer, 'claim_status']:,}")
print(f"  • Revenue Impact: ${insurance_performance.loc[top_performer, 'billed_amount']:,.0f}")

print(f"\nWorst Performer: {bottom_performer}")
print(f"  • Collection Rate: {bottom_rate:.1f}%")
print(f"  • Claims Volume: {insurance_performance.loc[bottom_performer, 'claim_status']:,}")
print(f"  • Revenue Impact: ${insurance_performance.loc[bottom_performer, 'billed_amount']:,.0f}")
print(f"  • Improvement Opportunity: ${insurance_performance.loc[bottom_performer, 'billed_amount'] * (top_rate - bottom_rate) / 100:,.0f}")

# Seasonal analysis
print(f"\n📅 SEASONAL PERFORMANCE ANALYSIS")
print("="*50)

# Monthly trends - fix the indexing issue
best_month_idx = monthly_efficiency['collection_efficiency'].idxmax()
worst_month_idx = monthly_efficiency['collection_efficiency'].idxmin()
best_month_value = monthly_efficiency.loc[best_month_idx, 'collection_efficiency']
worst_month_value = monthly_efficiency.loc[worst_month_idx, 'collection_efficiency']
best_month_period = monthly_efficiency.loc[best_month_idx, 'billing_month']
worst_month_period = monthly_efficiency.loc[worst_month_idx, 'billing_month']
monthly_volatility = monthly_efficiency['collection_efficiency'].std()

print(f"Monthly Performance Volatility: {monthly_volatility:.2f}%")
print(f"Best Month: {best_month_period} ({best_month_value:.1f}%)")
print(f"Worst Month: {worst_month_period} ({worst_month_value:.1f}%)")

# Calculate monthly improvement trend
monthly_trend = monthly_efficiency['collection_efficiency'].diff().mean()
if monthly_trend > 0:
    trend_direction = "improving"
    trend_icon = "📈"
else:
    trend_direction = "declining"
    trend_icon = "📉"

print(f"Overall Trend: {trend_icon} {trend_direction} ({monthly_trend:+.2f}% per month)")

# Denial analysis insights
print(f"\n🚨 DENIAL PATTERN ANALYSIS")
print("="*50)

# Calculate denial impact by category
denial_categories = {
    'Administrative': ['Duplicate Claim', 'Timely Filing Limit Exceeded', 'Claim Billed to Wrong Payer', 
                      'Missing or Incorrect Information', 'Invalid Place of Service'],
    'Authorization': ['Prior Authorization Required', 'Coordination of Benefits Issue'],
    'Coverage': ['Service Not Covered', 'Coverage Limit Exceeded', 'Out-of-Network Provider',
                'Expired or Invalid Insurance', 'Patient Not Eligible on DOS (Date of Service)'],
    'Clinical': ['Medical Necessity Denial', 'Incorrect Coding (ICD/CPT/HCPCS)']
}

category_impact = {}
for category, reasons in denial_categories.items():
    category_claims = denied_claims_df[denied_claims_df['denial_reason'].isin(reasons)]
    category_impact[category] = {
        'count': len(category_claims),
        'amount': category_claims['billed_amount'].sum(),
        'percentage': len(category_claims) / len(denied_claims_df) * 100
    }

print("Denial Categories by Impact:")
for category, impact in sorted(category_impact.items(), key=lambda x: x[1]['amount'], reverse=True):
    print(f"  • {category}:")
    print(f"    - Claims: {impact['count']:,} ({impact['percentage']:.1f}%)")
    print(f"    - Revenue Impact: ${impact['amount']:,.0f}")

# Revenue opportunity analysis
print(f"\n💰 REVENUE OPPORTUNITY ANALYSIS")
print("="*50)

# Calculate potential improvements
opportunities = {
    'Reduce Denials to 5%': (denial_rate - 5) / 100 * total_billed,
    'Improve Collection to 80%': (0.80 - collection_efficiency/100) * total_billed,
    'Reach Industry Benchmark (85%)': (0.85 - collection_efficiency/100) * total_billed,
    'Best-in-Class Performance (90%)': (0.90 - collection_efficiency/100) * total_billed,
    'Optimize A/R Management': total_billed * 0.05  # Assume 5% improvement from better A/R
}

total_opportunity = sum(max(0, opp) for opp in opportunities.values())

print("Revenue Improvement Opportunities:")
for opportunity, value in opportunities.items():
    if value > 0:
        print(f"  • {opportunity}: ${value:,.0f}")
        
print(f"\nTotal Opportunity Potential: ${total_opportunity:,.0f}")
print(f"As % of Current Collections: {total_opportunity/total_collected*100:.1f}%")

# DETAILED PERFORMANCE REPORT AND RECOMMENDATIONS
print("\n📋 DETAILED PERFORMANCE REPORT")
print("="*80)

# Risk assessment matrix
risk_factors = []
risk_scores = []

# Collection efficiency risk
if collection_efficiency < 70:
    risk_factors.append("Critical: Collection efficiency below 70%")
    risk_scores.append(10)
elif collection_efficiency < 80:
    risk_factors.append("High: Collection efficiency below 80%")
    risk_scores.append(7)
elif collection_efficiency < 85:
    risk_factors.append("Medium: Collection efficiency below benchmark")
    risk_scores.append(5)

# Denial rate risk
if denial_rate > 15:
    risk_factors.append("Critical: Denial rate above 15%")
    risk_scores.append(10)
elif denial_rate > 10:
    risk_factors.append("High: Denial rate above 10%")
    risk_scores.append(7)
elif denial_rate > 5:
    risk_factors.append("Medium: Denial rate above benchmark")
    risk_scores.append(5)

# A/R risk
if ar_percentage > 30:
    risk_factors.append("Critical: A/R above 30% of revenue")
    risk_scores.append(8)
elif ar_percentage > 20:
    risk_factors.append("Medium: A/R above 20% of revenue")
    risk_scores.append(5)

# Provider concentration risk
provider_concentration = insurance_performance['billed_amount'].max() / insurance_performance['billed_amount'].sum()
if provider_concentration > 0.3:
    risk_factors.append("Medium: High provider concentration risk")
    risk_scores.append(4)

overall_risk_score = sum(risk_scores) if risk_scores else 0

print("🎯 RISK ASSESSMENT")
print("="*50)
print(f"Overall Risk Score: {overall_risk_score}/50")

if overall_risk_score >= 25:
    risk_level = "🔴 CRITICAL RISK"
    risk_description = "Immediate intervention required"
elif overall_risk_score >= 15:
    risk_level = "🟡 HIGH RISK"
    risk_description = "Urgent attention needed"
elif overall_risk_score >= 8:
    risk_level = "🟠 MEDIUM RISK"
    risk_description = "Monitoring and improvement required"
else:
    risk_level = "🟢 LOW RISK"
    risk_description = "Performing within acceptable parameters"

print(f"Risk Level: {risk_level}")
print(f"Assessment: {risk_description}")

if risk_factors:
    print("\nIdentified Risk Factors:")
    for factor in risk_factors:
        print(f"  • {factor}")

# Performance trends analysis
print(f"\n📈 PERFORMANCE TRENDS ANALYSIS")
print("="*50)

# Calculate quarterly performance if enough data
quarters = claims_df.groupby(claims_df['claim_billing_date'].dt.to_period('Q')).agg({
    'billed_amount': 'sum',
    'paid_amount': 'sum',
    'billing_id': 'count'
})
quarters['efficiency'] = (quarters['paid_amount'] / quarters['billed_amount'] * 100).round(2)

if len(quarters) > 1:
    quarterly_trend = quarters['efficiency'].diff().mean()
    print("Quarterly Performance Trend:")
    for quarter, row in quarters.iterrows():
        print(f"  • {quarter}: {row['efficiency']:.1f}% efficiency, {row['billing_id']:,} claims, ${row['billed_amount']:,.0f} billed")
    
    if quarterly_trend > 0:
        print(f"\nTrend: 📈 Improving (+{quarterly_trend:.2f}% per quarter)")
    else:
        print(f"\nTrend: 📉 Declining ({quarterly_trend:.2f}% per quarter)")

# Payer mix analysis
print(f"\n🏥 PAYER MIX ANALYSIS")
print("="*50)

payer_mix = insurance_claims['insurance_provider'].value_counts(normalize=True) * 100
print("Current Payer Distribution:")
for payer, percentage in payer_mix.items():
    performance = insurance_performance.loc[payer, 'collection_rate']
    status = "✅" if performance > 60 else "⚠️" if performance > 55 else "❌"
    print(f"  • {payer}: {percentage:.1f}% of volume, {performance:.1f}% collection rate {status}")

# Calculate optimal payer mix based on performance
optimal_weights = insurance_performance['collection_rate'] / insurance_performance['collection_rate'].sum()
current_weights = payer_mix / 100
revenue_weighted_performance = (current_weights * insurance_performance['collection_rate']).sum()

print(f"\nPayer Mix Insights:")
print(f"  • Revenue-weighted collection rate: {revenue_weighted_performance:.1f}%")
print(f"  • Best performing payer: {insurance_performance['collection_rate'].idxmax()} ({insurance_performance['collection_rate'].max():.1f}%)")
print(f"  • Worst performing payer: {insurance_performance['collection_rate'].idxmin()} ({insurance_performance['collection_rate'].min():.1f}%)")

# Actionable recommendations
print(f"\n🎯 ACTIONABLE RECOMMENDATIONS")
print("="*50)

recommendations = []

# Collection efficiency recommendations
if collection_efficiency < 75:
    recommendations.append({
        'priority': 'HIGH',
        'category': 'Collection Efficiency',
        'action': 'Implement comprehensive revenue cycle optimization program',
        'timeline': '3-6 months',
        'impact': f"${(0.80 - collection_efficiency/100) * total_billed:,.0f} potential recovery"
    })

# Denial management recommendations
if denial_rate > 8:
    recommendations.append({
        'priority': 'HIGH',
        'category': 'Denial Management',
        'action': 'Deploy automated denial prevention and management system',
        'timeline': '2-4 months',
        'impact': f"${(denial_rate - 5)/100 * total_billed:,.0f} potential recovery"
    })

# Provider-specific recommendations
worst_performers = insurance_performance[insurance_performance['collection_rate'] < 58]
if len(worst_performers) > 0:
    recommendations.append({
        'priority': 'MEDIUM',
        'category': 'Payer Relations',
        'action': f'Renegotiate contracts with {len(worst_performers)} underperforming payers',
        'timeline': '6-12 months',
        'impact': f"${worst_performers['billed_amount'].sum() * 0.05:,.0f} estimated improvement"
    })

# A/R management recommendations
if ar_percentage > 25:
    recommendations.append({
        'priority': 'HIGH',
        'category': 'A/R Management',
        'action': 'Implement accelerated A/R collection program',
        'timeline': '1-3 months',
        'impact': f"${total_billed * 0.10:,.0f} potential cash flow improvement"
    })

# Administrative efficiency recommendations
admin_denials = sum(category_impact[cat]['count'] for cat in ['Administrative'] if cat in category_impact)
if admin_denials > total_insurance_claims * 0.03:  # More than 3% administrative denials
    recommendations.append({
        'priority': 'MEDIUM',
        'category': 'Process Improvement',
        'action': 'Implement automated claim validation and submission system',
        'timeline': '3-6 months',
        'impact': f"${category_impact.get('Administrative', {}).get('amount', 0) * 0.7:,.0f} preventable losses"
    })

# Sort recommendations by priority
priority_order = {'HIGH': 1, 'MEDIUM': 2, 'LOW': 3}
recommendations.sort(key=lambda x: priority_order[x['priority']])

print("Priority-Ranked Recommendations:")
for i, rec in enumerate(recommendations, 1):
    priority_icon = "🔥" if rec['priority'] == 'HIGH' else "⚡" if rec['priority'] == 'MEDIUM' else "💡"
    print(f"\n{i}. {priority_icon} {rec['priority']} PRIORITY: {rec['category']}")
    print(f"   Action: {rec['action']}")
    print(f"   Timeline: {rec['timeline']}")
    print(f"   Potential Impact: {rec['impact']}")

# ROI Analysis
print(f"\n💹 RETURN ON INVESTMENT ANALYSIS")
print("="*50)

# Estimate implementation costs and ROI
implementation_costs = {
    'Revenue Cycle Optimization': 500000,
    'Denial Management System': 300000,
    'A/R Management Program': 150000,
    'Process Automation': 400000,
    'Staff Training': 100000
}

total_implementation_cost = sum(implementation_costs.values())
total_annual_benefit = total_opportunity * 0.6  # Assume 60% of opportunity can be captured

roi_percentage = (total_annual_benefit - total_implementation_cost) / total_implementation_cost * 100
payback_period = total_implementation_cost / total_annual_benefit * 12  # in months

print(f"Investment Required: ${total_implementation_cost:,.0f}")
print(f"Estimated Annual Benefit: ${total_annual_benefit:,.0f}")
print(f"ROI: {roi_percentage:.0f}%")
print(f"Payback Period: {payback_period:.1f} months")

print(f"\nInvestment Breakdown:")
for investment, cost in implementation_costs.items():
    percentage = cost / total_implementation_cost * 100
    print(f"  • {investment}: ${cost:,.0f} ({percentage:.1f}%)")

# MONITORING FRAMEWORK AND SUCCESS METRICS
print("\n📊 MONITORING FRAMEWORK AND SUCCESS METRICS")
print("="*80)

# Define KPIs and targets
kpi_framework = {
    'Financial KPIs': {
        'Collection Efficiency': {
            'current': collection_efficiency,
            'target': 85,
            'benchmark': 90,
            'frequency': 'Monthly',
            'owner': 'Revenue Cycle Manager'
        },
        'Denial Rate': {
            'current': denial_rate,
            'target': 5,
            'benchmark': 3,
            'frequency': 'Monthly',
            'owner': 'Claims Manager'
        },
        'Days in A/R': {
            'current': 45,  # Estimated
            'target': 30,
            'benchmark': 25,
            'frequency': 'Monthly',
            'owner': 'A/R Manager'
        },
        'Bad Debt Ratio': {
            'current': bad_debt_ratio,
            'target': 3,
            'benchmark': 2,
            'frequency': 'Quarterly',
            'owner': 'CFO'
        }
    },
    'Operational KPIs': {
        'First Pass Payment Rate': {
            'current': 90,  # Estimated
            'target': 95,
            'benchmark': 98,
            'frequency': 'Weekly',
            'owner': 'Claims Manager'
        },
        'Prior Auth Approval Rate': {
            'current': 85,  # Estimated
            'target': 95,
            'benchmark': 98,
            'frequency': 'Weekly',
            'owner': 'Authorization Team'
        },
        'Claims Submission Time': {
            'current': 3,  # Days - estimated
            'target': 1,
            'benchmark': 0.5,
            'frequency': 'Daily',
            'owner': 'Billing Manager'
        }
    },
    'Quality KPIs': {
        'Clean Claim Rate': {
            'current': 89,  # Estimated from approval rate
            'target': 95,
            'benchmark': 98,
            'frequency': 'Daily',
            'owner': 'Quality Manager'
        },
        'Documentation Accuracy': {
            'current': 85,  # Estimated
            'target': 95,
            'benchmark': 98,
            'frequency': 'Weekly',
            'owner': 'Coding Manager'
        }
    }
}

print("🎯 KEY PERFORMANCE INDICATORS (KPIs)")
print("="*50)

for category, kpis in kpi_framework.items():
    print(f"\n{category}:")
    for kpi_name, kpi_data in kpis.items():
        current = kpi_data['current']
        target = kpi_data['target']
        benchmark = kpi_data['benchmark']
        
        # Calculate performance status
        if isinstance(current, float) and kpi_name in ['Denial Rate', 'Bad Debt Ratio', 'Days in A/R', 'Claims Submission Time']:
            # Lower is better
            target_performance = (target / current * 100) if current > 0 else 100
            benchmark_performance = (benchmark / current * 100) if current > 0 else 100
        else:
            # Higher is better
            target_performance = (current / target * 100) if target > 0 else 100
            benchmark_performance = (current / benchmark * 100) if benchmark > 0 else 100
        
        status = "🟢" if target_performance >= 100 else "🟡" if target_performance >= 80 else "🔴"
        
        print(f"  • {kpi_name}: {current:.1f} {status}")
        print(f"    Target: {target:.1f} | Benchmark: {benchmark:.1f}")
        print(f"    Owner: {kpi_data['owner']} | Frequency: {kpi_data['frequency']}")

# Improvement roadmap
print(f"\n🗺️ IMPROVEMENT ROADMAP")
print("="*50)

roadmap_phases = {
    'Phase 1 (Months 1-3): Foundation': [
        'Implement daily KPI monitoring dashboard',
        'Establish denial management workflows',
        'Deploy automated claim validation',
        'Train staff on new processes',
        'Begin A/R cleanup initiative'
    ],
    'Phase 2 (Months 4-6): Optimization': [
        'Deploy advanced analytics for denial prediction',
        'Implement real-time eligibility verification',
        'Negotiate improved payer contracts',
        'Enhance prior authorization processes',
        'Optimize patient financial counseling'
    ],
    'Phase 3 (Months 7-12): Excellence': [
        'Achieve target KPI performance',
        'Implement predictive analytics',
        'Deploy AI-powered revenue cycle tools',
        'Establish center of excellence',
        'Benchmark against industry leaders'
    ]
}

for phase, activities in roadmap_phases.items():
    print(f"\n{phase}:")
    for activity in activities:
        print(f"  ✓ {activity}")

# Success metrics and milestones
print(f"\n🏆 SUCCESS METRICS AND MILESTONES")
print("="*50)

milestones = [
    {'timeline': 'Month 3', 'metric': 'Collection Efficiency', 'target': 70, 'impact': f"${(0.70 - collection_efficiency/100) * total_billed * 0.25:,.0f}"},
    {'timeline': 'Month 6', 'metric': 'Collection Efficiency', 'target': 75, 'impact': f"${(0.75 - collection_efficiency/100) * total_billed * 0.5:,.0f}"},
    {'timeline': 'Month 9', 'metric': 'Collection Efficiency', 'target': 80, 'impact': f"${(0.80 - collection_efficiency/100) * total_billed * 0.75:,.0f}"},
    {'timeline': 'Month 12', 'metric': 'Collection Efficiency', 'target': 85, 'impact': f"${(0.85 - collection_efficiency/100) * total_billed:,.0f}"},
    {'timeline': 'Month 6', 'metric': 'Denial Rate', 'target': 7, 'impact': f"${(denial_rate - 7)/100 * total_billed * 0.5:,.0f}"},
    {'timeline': 'Month 12', 'metric': 'Denial Rate', 'target': 5, 'impact': f"${(denial_rate - 5)/100 * total_billed:,.0f}"}
]

print("Performance Milestones:")
for milestone in milestones:
    print(f"  • {milestone['timeline']}: {milestone['metric']} reaches {milestone['target']}%")
    print(f"    Expected Impact: {milestone['impact']}")

# Monitoring and reporting schedule
print(f"\n📅 MONITORING AND REPORTING SCHEDULE")
print("="*50)

reporting_schedule = {
    'Daily': ['Claims submission volume', 'Denial alerts', 'A/R aging'],
    'Weekly': ['Collection efficiency', 'Denial trends', 'Payer performance'],
    'Monthly': ['Full KPI dashboard', 'Financial performance', 'Trend analysis'],
    'Quarterly': ['Strategic review', 'ROI assessment', 'Benchmark comparison'],
    'Annually': ['Comprehensive audit', 'Contract renegotiation', 'System upgrades']
}

for frequency, reports in reporting_schedule.items():
    print(f"\n{frequency} Reports:")
    for report in reports:
        print(f"  • {report}")

# Final summary and next steps
print(f"\n📋 EXECUTIVE SUMMARY AND NEXT STEPS")
print("="*80)

print("CURRENT STATE:")
print(f"  • Collection Efficiency: {collection_efficiency:.1f}% (Target: 85%)")
print(f"  • Denial Rate: {denial_rate:.1f}% (Target: 5%)")
print(f"  • Outstanding A/R: ${(total_billed - total_collected)/1000000:.1f}M")
print(f"  • Risk Level: {risk_level}")

print(f"\nOPPORTUNITY:")
print(f"  • Total Revenue Opportunity: ${total_opportunity/1000000:.1f}M")
print(f"  • 12-Month Target Impact: ${total_annual_benefit/1000000:.1f}M")
print(f"  • ROI: {roi_percentage:.0f}% with {payback_period:.1f} month payback")

print(f"\nIMMEDIATE NEXT STEPS (Next 30 Days):")
immediate_actions = [
    "1. Establish Revenue Cycle Improvement Committee",
    "2. Implement daily KPI monitoring dashboard",
    "3. Begin denial root cause analysis for top 5 reasons",
    "4. Initiate A/R cleanup for accounts >90 days",
    "5. Schedule payer performance review meetings",
    "6. Develop staff training curriculum",
    "7. Create monthly performance review schedule"
]

for action in immediate_actions:
    print(f"  {action}")

print(f"\n" + "="*80)
print("ANALYSIS COMPLETE - READY FOR IMPLEMENTATION")
print("="*80)

# NEXT YEAR REVENUE PREDICTION AND FORECASTING
print("\n🔮 NEXT YEAR REVENUE PREDICTION AND FORECASTING")
print("="*80)

from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import numpy as np

# Generate next 12 months starting from June 2025
current_date = datetime(2025, 6, 1)  # Starting from June since data goes to May
forecast_months = []
for i in range(12):
    month_date = current_date + relativedelta(months=i)
    forecast_months.append(month_date.strftime('%Y-%m'))

print("📅 FORECAST PERIOD")
print("="*50)
print(f"Forecast Period: {forecast_months[0]} to {forecast_months[-1]}")
print(f"Base Period Used: {claims_df['claim_billing_date'].min().strftime('%Y-%m')} to {claims_df['claim_billing_date'].max().strftime('%Y-%m')}")

# Calculate baseline metrics from historical data
historical_monthly_avg = {
    'claims_volume': monthly_summary['claim_status'].mean(),
    'billed_amount': monthly_summary['billed_amount'].mean(),
    'collection_efficiency': monthly_efficiency['collection_efficiency'].mean(),
    'denial_rate': monthly_denials['Denial_Count'].sum() / monthly_summary['claim_status'].sum() * 100
}

print(f"\n📊 BASELINE HISTORICAL METRICS")
print("="*50)
for metric, value in historical_monthly_avg.items():
    if 'amount' in metric:
        print(f"  • {metric.replace('_', ' ').title()}: ${value:,.0f}")
    elif 'rate' in metric or 'efficiency' in metric:
        print(f"  • {metric.replace('_', ' ').title()}: {value:.1f}%")
    else:
        print(f"  • {metric.replace('_', ' ').title()}: {value:,.0f}")

# Create multiple forecast scenarios
scenarios = {
    'Conservative': {
        'description': 'Minimal improvements, current trend continues',
        'volume_growth': 0.02,  # 2% annual growth
        'efficiency_improvement': 0.1,  # 0.1% monthly improvement
        'denial_reduction': 0.05,  # 0.05% monthly reduction
        'seasonal_factor': 1.0
    },
    'Realistic': {
        'description': 'Moderate improvements with planned initiatives',
        'volume_growth': 0.05,  # 5% annual growth
        'efficiency_improvement': 0.5,  # 0.5% monthly improvement
        'denial_reduction': 0.2,  # 0.2% monthly reduction
        'seasonal_factor': 1.0
    },
    'Optimistic': {
        'description': 'Aggressive improvements with full optimization',
        'volume_growth': 0.08,  # 8% annual growth
        'efficiency_improvement': 1.0,  # 1.0% monthly improvement
        'denial_reduction': 0.4,  # 0.4% monthly reduction
        'seasonal_factor': 1.0
    }
}

# Seasonal patterns based on historical data (simplified)
seasonal_patterns = {
    1: 0.95,   # January - lower due to holidays
    2: 1.05,   # February - catch up
    3: 1.10,   # March - peak
    4: 1.08,   # April - high
    5: 0.92,   # May - lower (from data)
    6: 1.02,   # June - normal
    7: 0.98,   # July - vacation impact
    8: 0.96,   # August - vacation impact
    9: 1.05,   # September - back to normal
    10: 1.08,  # October - high
    11: 1.03,  # November - normal
    12: 0.98   # December - holiday impact
}

# Generate forecasts for each scenario
forecast_results = {}

for scenario_name, scenario_params in scenarios.items():
    monthly_forecasts = []
    
    # Starting values
    current_volume = historical_monthly_avg['claims_volume']
    current_efficiency = historical_monthly_avg['collection_efficiency']
    current_denial_rate = historical_monthly_avg['denial_rate']
    
    for i, month in enumerate(forecast_months):
        month_num = int(month.split('-')[1])
        
        # Apply volume growth
        monthly_volume = current_volume * (1 + scenario_params['volume_growth'] * i / 12)
        
        # Apply seasonal adjustment
        seasonal_adj = seasonal_patterns.get(month_num, 1.0)
        monthly_volume *= seasonal_adj
        
        # Apply efficiency improvements
        monthly_efficiency = min(90, current_efficiency + (scenario_params['efficiency_improvement'] * i))
        
        # Apply denial rate reduction
        monthly_denial_rate = max(3, current_denial_rate - (scenario_params['denial_reduction'] * i))
        
        # Calculate billed amount (assuming avg claim value increases 3% annually)
        avg_claim_growth = 1 + (0.03 * i / 12)
        monthly_billed = monthly_volume * avg_claim_value * avg_claim_growth
        
        # Calculate collected amount
        monthly_collected = monthly_billed * (monthly_efficiency / 100)
        
        # Calculate denied amount
        monthly_denied = monthly_billed * (monthly_denial_rate / 100)
        
        monthly_forecasts.append({
            'month': month,
            'volume': int(monthly_volume),
            'billed_amount': monthly_billed,
            'collected_amount': monthly_collected,
            'denied_amount': monthly_denied,
            'efficiency': monthly_efficiency,
            'denial_rate': monthly_denial_rate
        })
    
    forecast_results[scenario_name] = monthly_forecasts

print(f"\n📈 SCENARIO-BASED FORECASTS")
print("="*50)

# Summary table for each scenario
for scenario_name, forecasts in forecast_results.items():
    total_billed = sum(f['billed_amount'] for f in forecasts)
    total_collected = sum(f['collected_amount'] for f in forecasts)
    total_denied = sum(f['denied_amount'] for f in forecasts)
    avg_efficiency = np.mean([f['efficiency'] for f in forecasts])
    
    print(f"\n{scenario_name.upper()} SCENARIO:")
    print(f"  Description: {scenarios[scenario_name]['description']}")
    print(f"  • Total Billed: ${total_billed:,.0f}")
    print(f"  • Total Collected: ${total_collected:,.0f}")
    print(f"  • Total Denied: ${total_denied:,.0f}")
    print(f"  • Average Efficiency: {avg_efficiency:.1f}%")
    print(f"  • vs Current Year: {(total_collected - total_collected)/total_collected*100:+.1f}%")

# Detailed monthly breakdown for realistic scenario
print(f"\n📅 DETAILED MONTHLY FORECAST - REALISTIC SCENARIO")
print("="*50)
print("Month".ljust(8) + "Volume".rjust(8) + "Billed($M)".rjust(12) + "Collected($M)".rjust(15) + "Efficiency(%)".rjust(15))
print("-" * 58)

realistic_forecasts = forecast_results['Realistic']
for forecast in realistic_forecasts:
    month = forecast['month']
    volume = f"{forecast['volume']:,}"
    billed = f"${forecast['billed_amount']/1000000:.1f}M"
    collected = f"${forecast['collected_amount']/1000000:.1f}M"
    efficiency = f"{forecast['efficiency']:.1f}%"
    
    print(month.ljust(8) + volume.rjust(8) + billed.rjust(12) + collected.rjust(15) + efficiency.rjust(15))

# REVENUE PREDICTION VISUALIZATIONS AND FINANCIAL PROJECTIONS
print(f"\n📊 REVENUE PREDICTION VISUALIZATIONS")
print("="*80)

# Create comprehensive forecast visualizations
fig, axes = plt.subplots(3, 2, figsize=(18, 16))
fig.suptitle('Next Year Revenue Forecasting Analysis', fontsize=16, fontweight='bold')

# 1. Monthly Revenue Forecast Comparison
months_short = [m.split('-')[1] for m in forecast_months]
for scenario_name, forecasts in forecast_results.items():
    collected_amounts = [f['collected_amount']/1000000 for f in forecasts]
    axes[0,0].plot(months_short, collected_amounts, marker='o', linewidth=2, label=scenario_name)

axes[0,0].set_title('Monthly Collected Revenue Forecast')
axes[0,0].set_xlabel('Month')
axes[0,0].set_ylabel('Collected Revenue ($M)')
axes[0,0].legend()
axes[0,0].grid(True, alpha=0.3)

# 2. Collection Efficiency Improvement Trajectory
for scenario_name, forecasts in forecast_results.items():
    efficiency_values = [f['efficiency'] for f in forecasts]
    axes[0,1].plot(months_short, efficiency_values, marker='s', linewidth=2, label=scenario_name)

axes[0,1].set_title('Collection Efficiency Improvement')
axes[0,1].set_xlabel('Month')
axes[0,1].set_ylabel('Collection Efficiency (%)')
axes[0,1].legend()
axes[0,1].grid(True, alpha=0.3)

# 3. Cumulative Revenue Comparison
scenarios_cumulative = {}
for scenario_name, forecasts in forecast_results.items():
    cumulative_collected = np.cumsum([f['collected_amount'] for f in forecasts]) / 1000000
    scenarios_cumulative[scenario_name] = cumulative_collected
    axes[1,0].plot(months_short, cumulative_collected, marker='^', linewidth=3, label=scenario_name)

axes[1,0].set_title('Cumulative Collected Revenue')
axes[1,0].set_xlabel('Month')
axes[1,0].set_ylabel('Cumulative Revenue ($M)')
axes[1,0].legend()
axes[1,0].grid(True, alpha=0.3)

# 4. Denial Rate Reduction Forecast
for scenario_name, forecasts in forecast_results.items():
    denial_rates = [f['denial_rate'] for f in forecasts]
    axes[1,1].plot(months_short, denial_rates, marker='d', linewidth=2, label=scenario_name)

axes[1,1].set_title('Denial Rate Reduction Forecast')
axes[1,1].set_xlabel('Month')
axes[1,1].set_ylabel('Denial Rate (%)')
axes[1,1].legend()
axes[1,1].grid(True, alpha=0.3)

# 5. Annual Revenue Comparison (Bar Chart)
scenario_names = list(forecast_results.keys())
annual_billed = [sum(f['billed_amount'] for f in forecasts)/1000000 for forecasts in forecast_results.values()]
annual_collected = [sum(f['collected_amount'] for f in forecasts)/1000000 for forecasts in forecast_results.values()]

x_pos = np.arange(len(scenario_names))
width = 0.35
bars1 = axes[2,0].bar(x_pos - width/2, annual_billed, width, label='Billed', alpha=0.8, color='lightblue')
bars2 = axes[2,0].bar(x_pos + width/2, annual_collected, width, label='Collected', alpha=0.8, color='lightgreen')

axes[2,0].set_title('Annual Revenue Forecast by Scenario')
axes[2,0].set_xlabel('Scenario')
axes[2,0].set_ylabel('Revenue ($M)')
axes[2,0].set_xticks(x_pos)
axes[2,0].set_xticklabels(scenario_names)
axes[2,0].legend()

# Add value labels on bars
for bars in [bars1, bars2]:
    for bar in bars:
        height = bar.get_height()
        axes[2,0].text(bar.get_x() + bar.get_width()/2., height + 1,
                      f'${height:.0f}M', ha='center', va='bottom', fontsize=9)

# 6. Revenue Opportunity Gap Analysis
current_annual_collected = total_collected / 1000000  # Convert current to annual estimate
forecast_improvements = [annual_collected[i] - current_annual_collected for i in range(len(scenario_names))]
colors_improvement = ['red' if x < 0 else 'green' for x in forecast_improvements]

axes[2,1].bar(scenario_names, forecast_improvements, color=colors_improvement, alpha=0.7)
axes[2,1].set_title('Revenue Improvement vs Current Year')
axes[2,1].set_xlabel('Scenario')
axes[2,1].set_ylabel('Revenue Improvement ($M)')
axes[2,1].axhline(y=0, color='black', linestyle='-', alpha=0.5)

for i, improvement in enumerate(forecast_improvements):
    axes[2,1].text(i, improvement + (1 if improvement > 0 else -1),
                  f'${improvement:+.1f}M', ha='center', va='bottom' if improvement > 0 else 'top')

plt.tight_layout()
plt.show()

# Financial Impact Analysis
print(f"\n💰 FINANCIAL IMPACT ANALYSIS")
print("="*50)

# Calculate key financial metrics for each scenario
financial_summary = {}
current_year_estimate = total_collected * 2.4  # Annualize current 5-month data

for scenario_name, forecasts in forecast_results.items():
    annual_collected = sum(f['collected_amount'] for f in forecasts)
    annual_billed = sum(f['billed_amount'] for f in forecasts)
    annual_denied = sum(f['denied_amount'] for f in forecasts)
    
    improvement_vs_current = annual_collected - current_year_estimate
    collection_efficiency = annual_collected / annual_billed * 100
    
    financial_summary[scenario_name] = {
        'annual_collected': annual_collected,
        'annual_billed': annual_billed,
        'improvement_vs_current': improvement_vs_current,
        'collection_efficiency': collection_efficiency,
        'denied_amount': annual_denied
    }

# Display financial summary
for scenario, metrics in financial_summary.items():
    print(f"\n{scenario.upper()} SCENARIO FINANCIAL SUMMARY:")
    print(f"  • Total Revenue Billed: ${metrics['annual_billed']:,.0f}")
    print(f"  • Total Revenue Collected: ${metrics['annual_collected']:,.0f}")
    print(f"  • Collection Efficiency: {metrics['collection_efficiency']:.1f}%")
    print(f"  • Improvement vs Current: ${metrics['improvement_vs_current']:+,.0f}")
    print(f"  • Denied Revenue: ${metrics['denied_amount']:,.0f}")
    print(f"  • ROI vs Current: {metrics['improvement_vs_current']/current_year_estimate*100:+.1f}%")

# Risk-adjusted projections
print(f"\n⚖️ RISK-ADJUSTED PROJECTIONS")
print("="*50)

# Apply probability weights to scenarios
scenario_probabilities = {
    'Conservative': 0.3,   # 30% chance
    'Realistic': 0.5,      # 50% chance  
    'Optimistic': 0.2      # 20% chance
}

# Calculate weighted average projection
weighted_collected = sum(financial_summary[scenario]['annual_collected'] * prob 
                        for scenario, prob in scenario_probabilities.items())
weighted_billed = sum(financial_summary[scenario]['annual_billed'] * prob 
                     for scenario, prob in scenario_probabilities.items())
weighted_efficiency = weighted_collected / weighted_billed * 100

print("PROBABILITY-WEIGHTED FORECAST:")
print(f"  • Expected Revenue Collected: ${weighted_collected:,.0f}")
print(f"  • Expected Revenue Billed: ${weighted_billed:,.0f}")
print(f"  • Expected Collection Efficiency: {weighted_efficiency:.1f}%")
print(f"  • Expected Improvement: ${weighted_collected - current_year_estimate:+,.0f}")

# Confidence intervals (Monte Carlo simulation approach)
np.random.seed(42)
monte_carlo_results = []

for _ in range(1000):
    # Random scenario selection based on probabilities
    scenario_choice = np.random.choice(list(scenario_probabilities.keys()), 
                                     p=list(scenario_probabilities.values()))
    
    # Add random variation (±10%)
    base_collected = financial_summary[scenario_choice]['annual_collected']
    variation = np.random.normal(0, 0.1)  # 10% standard deviation
    simulated_result = base_collected * (1 + variation)
    monte_carlo_results.append(simulated_result)

confidence_95_lower = np.percentile(monte_carlo_results, 2.5)
confidence_95_upper = np.percentile(monte_carlo_results, 97.5)
confidence_80_lower = np.percentile(monte_carlo_results, 10)
confidence_80_upper = np.percentile(monte_carlo_results, 90)

print(f"\nCONFIDENCE INTERVALS:")
print(f"  • 95% Confidence Range: ${confidence_95_lower:,.0f} - ${confidence_95_upper:,.0f}")
print(f"  • 80% Confidence Range: ${confidence_80_lower:,.0f} - ${confidence_80_upper:,.0f}")
print(f"  • Most Likely Outcome: ${np.median(monte_carlo_results):,.0f}")

# Monthly cash flow projections
print(f"\n💸 MONTHLY CASH FLOW PROJECTIONS (REALISTIC SCENARIO)")
print("="*50)

realistic_monthly = forecast_results['Realistic']
cumulative_cash = 0
print("Month".ljust(8) + "Monthly($M)".rjust(12) + "Cumulative($M)".rjust(15) + "YoY Growth(%)".rjust(15))
print("-" * 50)

for i, forecast in enumerate(realistic_monthly):
    monthly_cash = forecast['collected_amount'] / 1000000
    cumulative_cash += monthly_cash
    
    # Calculate YoY growth (assuming similar seasonal pattern in previous year)
    prev_year_estimate = (total_collected * 2.4) / 12  # Monthly average from current year
    yoy_growth = (monthly_cash - prev_year_estimate/1000000) / (prev_year_estimate/1000000) * 100
    
    month = forecast['month'].split('-')[1]
    print(f"{month}".ljust(8) + f"${monthly_cash:.1f}M".rjust(12) + f"${cumulative_cash:.1f}M".rjust(15) + f"{yoy_growth:+.1f}%".rjust(15))

# STRATEGIC REVENUE FORECAST RECOMMENDATIONS
print(f"\n🎯 STRATEGIC RECOMMENDATIONS FOR REVENUE FORECASTS")
print("="*80)

# Calculate implementation requirements for each scenario
implementation_requirements = {
    'Conservative': {
        'required_actions': [
            'Basic process improvements',
            'Staff training on denial reduction',
            'Monthly performance monitoring'
        ],
        'investment_needed': 250000,
        'timeline': '3-6 months',
        'success_probability': 85
    },
    'Realistic': {
        'required_actions': [
            'Revenue cycle optimization program',
            'Automated denial management system',
            'Enhanced A/R management',
            'Payer contract renegotiation'
        ],
        'investment_needed': 750000,
        'timeline': '6-12 months',
        'success_probability': 70
    },
    'Optimistic': {
        'required_actions': [
            'Complete RCM transformation',
            'AI-powered analytics implementation',
            'Advanced automation deployment',
            'Strategic payer partnerships',
            'Center of excellence establishment'
        ],
        'investment_needed': 1500000,
        'timeline': '12-18 months',
        'success_probability': 45
    }
}

print("IMPLEMENTATION ROADMAP BY SCENARIO:")
print("="*50)

for scenario, requirements in implementation_requirements.items():
    projected_improvement = financial_summary[scenario]['improvement_vs_current']
    roi = (projected_improvement - requirements['investment_needed']) / requirements['investment_needed'] * 100
    
    print(f"\n{scenario.upper()} SCENARIO:")
    print(f"  Investment Required: ${requirements['investment_needed']:,}")
    print(f"  Timeline: {requirements['timeline']}")
    print(f"  Success Probability: {requirements['success_probability']}%")
    print(f"  Expected ROI: {roi:.0f}%")
    print(f"  Required Actions:")
    for action in requirements['required_actions']:
        print(f"    • {action}")

# Quarterly milestone tracking
print(f"\n📅 QUARTERLY MILESTONE TRACKING")
print("="*50)

# Define quarterly targets for realistic scenario
realistic_annual = financial_summary['Realistic']['annual_collected']
quarterly_targets = {
    'Q1 2025-26': {
        'target_efficiency': 68,
        'target_collected': realistic_annual * 0.22,  # 22% in Q1
        'key_initiatives': ['Denial management deployment', 'Staff training completion']
    },
    'Q2 2025-26': {
        'target_efficiency': 72,
        'target_collected': realistic_annual * 0.24,  # 24% in Q2
        'key_initiatives': ['A/R optimization', 'Process automation']
    },
    'Q3 2025-26': {
        'target_efficiency': 76,
        'target_collected': realistic_annual * 0.26,  # 26% in Q3
        'key_initiatives': ['Payer negotiations', 'Advanced analytics']
    },
    'Q4 2025-26': {
        'target_efficiency': 80,
        'target_collected': realistic_annual * 0.28,  # 28% in Q4
        'key_initiatives': ['Performance optimization', 'Best practice implementation']
    }
}

print("REALISTIC SCENARIO - QUARTERLY MILESTONES:")
for quarter, targets in quarterly_targets.items():
    print(f"\n{quarter}:")
    print(f"  • Target Collection Efficiency: {targets['target_efficiency']}%")
    print(f"  • Target Quarterly Revenue: ${targets['target_collected']:,.0f}")
    print(f"  • Key Initiatives:")
    for initiative in targets['key_initiatives']:
        print(f"    - {initiative}")

# Risk mitigation strategies
print(f"\n⚠️ RISK MITIGATION STRATEGIES")
print("="*50)

risk_factors = {
    'Economic Downturn': {
        'probability': 25,
        'impact': 'Reduce volumes by 10-15%',
        'mitigation': 'Diversify payer mix, focus on essential services'
    },
    'Regulatory Changes': {
        'probability': 40,
        'impact': 'Potential reimbursement rate changes',
        'mitigation': 'Stay updated on regulations, maintain compliance'
    },
    'Technology Implementation Delays': {
        'probability': 30,
        'impact': 'Delayed efficiency improvements',
        'mitigation': 'Phased implementation, vendor SLAs'
    },
    'Staff Turnover': {
        'probability': 35,
        'impact': 'Temporary efficiency reduction',
        'mitigation': 'Comprehensive training, retention programs'
    },
    'Payer Contract Changes': {
        'probability': 50,
        'impact': 'Rate adjustments, new requirements',
        'mitigation': 'Proactive negotiations, contract monitoring'
    }
}

print("IDENTIFIED RISKS AND MITIGATION PLANS:")
for risk, details in risk_factors.items():
    print(f"\n• {risk} (Probability: {details['probability']}%)")
    print(f"  Impact: {details['impact']}")
    print(f"  Mitigation: {details['mitigation']}")

# Sensitivity analysis
print(f"\n📊 SENSITIVITY ANALYSIS")
print("="*50)

# Test sensitivity to key variables
base_scenario = financial_summary['Realistic']
sensitivity_tests = {
    'Volume -10%': base_scenario['annual_collected'] * 0.9,
    'Volume +10%': base_scenario['annual_collected'] * 1.1,
    'Efficiency -2%': base_scenario['annual_collected'] * 0.98,
    'Efficiency +2%': base_scenario['annual_collected'] * 1.02,
    'Avg Claim Value -5%': base_scenario['annual_collected'] * 0.95,
    'Avg Claim Value +5%': base_scenario['annual_collected'] * 1.05
}

print("SENSITIVITY TO KEY VARIABLES (Realistic Scenario Base):")
print("Variable Change".ljust(20) + "Revenue Impact".rjust(15) + "$ Change".rjust(15))
print("-" * 50)

for test_name, result in sensitivity_tests.items():
    impact = (result - base_scenario['annual_collected']) / base_scenario['annual_collected'] * 100
    dollar_change = result - base_scenario['annual_collected']
    print(f"{test_name}".ljust(20) + f"{impact:+.1f}%".rjust(15) + f"${dollar_change:+,.0f}".rjust(15))

# Final recommendations and action plan
print(f"\n🎯 FINAL RECOMMENDATIONS AND ACTION PLAN")
print("="*80)

print("RECOMMENDED APPROACH:")
print("1. TARGET THE REALISTIC SCENARIO as the primary goal")
print("   • Expected Revenue: ${:,.0f}".format(financial_summary['Realistic']['annual_collected']))
print("   • Collection Efficiency: {:.1f}%".format(financial_summary['Realistic']['collection_efficiency']))
print("   • Investment Required: ${:,}".format(implementation_requirements['Realistic']['investment_needed']))
print("   • Expected ROI: {:.0f}%".format((financial_summary['Realistic']['improvement_vs_current'] - implementation_requirements['Realistic']['investment_needed']) / implementation_requirements['Realistic']['investment_needed'] * 100))

print(f"\n2. IMPLEMENT PHASED APPROACH:")
phase_plan = {
    'Phase 1 (Months 1-3)': [
        'Deploy denial management system',
        'Implement daily KPI monitoring',
        'Begin A/R cleanup initiative',
        'Start staff training programs'
    ],
    'Phase 2 (Months 4-8)': [
        'Optimize collection processes',
        'Renegotiate key payer contracts',
        'Deploy automation tools',
        'Establish performance metrics'
    ],
    'Phase 3 (Months 9-12)': [
        'Fine-tune all systems',
        'Implement advanced analytics',
        'Achieve target efficiency rates',
        'Plan for continuous improvement'
    ]
}

for phase, activities in phase_plan.items():
    print(f"\n   {phase}:")
    for activity in activities:
        print(f"     • {activity}")

print(f"\n3. MONITOR AND ADJUST:")
print("   • Weekly performance reviews")
print("   • Monthly forecast updates")
print("   • Quarterly strategy adjustments")
print("   • Annual comprehensive review")

print(f"\n4. SUCCESS METRICS:")
success_metrics = [
    'Collection efficiency improvement: 64.5% → 80%+ by year-end',
    'Denial rate reduction: 10.1% → 6%+ by year-end',
    'Revenue increase: ${:+,.0f} over current year'.format(financial_summary['Realistic']['improvement_vs_current']),
    'ROI achievement: 200%+ on investments made'
]

for metric in success_metrics:
    print(f"   • {metric}")

print(f"\n" + "="*80)
print("REVENUE FORECAST ANALYSIS COMPLETE")
print("RECOMMENDED NEXT STEP: EXECUTIVE APPROVAL FOR REALISTIC SCENARIO IMPLEMENTATION")
print("="*80)

# STRATEGIC RECOMMENDATIONS VISUALIZATIONS
print(f"\n📊 STRATEGIC RECOMMENDATIONS VISUALIZATIONS")
print("="*80)

# Import required modules
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.gridspec import GridSpec

# Create comprehensive dashboard for strategic recommendations
fig = plt.figure(figsize=(20, 24))
gs = GridSpec(6, 4, figure=fig, hspace=0.4, wspace=0.3)

# 1. ROI Comparison by Scenario
ax1 = fig.add_subplot(gs[0, :2])
scenarios = ['Conservative', 'Realistic', 'Optimistic']
investments = [250000, 750000, 1500000]
improvements = [
    financial_summary['Conservative']['improvement_vs_current'],
    financial_summary['Realistic']['improvement_vs_current'], 
    financial_summary['Optimistic']['improvement_vs_current']
]
roi_values = [(imp - inv) / inv * 100 for imp, inv in zip(improvements, investments)]

colors_roi = ['#ff6b6b', '#4ecdc4', '#45b7d1']
bars = ax1.bar(scenarios, roi_values, color=colors_roi, alpha=0.8)
ax1.set_title('ROI by Implementation Scenario', fontsize=14, fontweight='bold')
ax1.set_ylabel('ROI (%)')
ax1.grid(True, alpha=0.3)

# Add value labels on bars
for bar, roi in zip(bars, roi_values):
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + (max(roi_values) * 0.01),
             f'{roi:.0f}%', ha='center', va='bottom', fontweight='bold')

# 2. Implementation Timeline and Costs
ax2 = fig.add_subplot(gs[0, 2:])
timeline_months = [6, 12, 18]
scenario_costs = [250000, 750000, 1500000]
success_prob = [85, 70, 45]

# Create timeline chart
scatter = ax2.scatter(timeline_months, scenario_costs, s=[p*10 for p in success_prob], 
                     c=colors_roi, alpha=0.7)
ax2.set_title('Implementation Timeline vs Investment\n(Bubble size = Success Probability)', 
              fontsize=14, fontweight='bold')
ax2.set_xlabel('Timeline (Months)')
ax2.set_ylabel('Investment Required ($)')
ax2.grid(True, alpha=0.3)

# Add scenario labels
for i, scenario in enumerate(scenarios):
    ax2.annotate(f'{scenario}\n{success_prob[i]}% success', 
                (timeline_months[i], scenario_costs[i]),
                xytext=(10, 10), textcoords='offset points',
                fontweight='bold', ha='left')

# 3. Quarterly Milestone Progress Chart
ax3 = fig.add_subplot(gs[1, :])
quarters = ['Q1 2025-26', 'Q2 2025-26', 'Q3 2025-26', 'Q4 2025-26']
target_efficiency = [68, 72, 76, 80]
target_collected = [32.9, 35.7, 38.7, 41.7]  # in millions

ax3_twin = ax3.twinx()

# Efficiency line
line1 = ax3.plot(quarters, target_efficiency, 'o-', color='#4ecdc4', linewidth=3, 
                markersize=8, label='Collection Efficiency (%)')
ax3.set_ylabel('Collection Efficiency (%)', color='#4ecdc4', fontweight='bold')
ax3.tick_params(axis='y', labelcolor='#4ecdc4')

# Revenue bars
bars3 = ax3_twin.bar(quarters, target_collected, alpha=0.6, color='#45b7d1', 
                     label='Quarterly Revenue ($M)')
ax3_twin.set_ylabel('Quarterly Revenue ($M)', color='#45b7d1', fontweight='bold')
ax3_twin.tick_params(axis='y', labelcolor='#45b7d1')

ax3.set_title('Quarterly Milestone Tracking - Realistic Scenario', fontsize=14, fontweight='bold')
ax3.grid(True, alpha=0.3)

# Add value labels
for i, (eff, rev) in enumerate(zip(target_efficiency, target_collected)):
    ax3.text(i, eff + 0.5, f'{eff}%', ha='center', va='bottom', fontweight='bold', color='#4ecdc4')
    ax3_twin.text(i, rev + 0.5, f'${rev}M', ha='center', va='bottom', fontweight='bold', color='#45b7d1')

# 4. Risk Factor Analysis Heatmap
ax4 = fig.add_subplot(gs[2, :2])
risk_data = [
    ['Economic Downturn', 25, 'High'],
    ['Regulatory Changes', 40, 'Medium'], 
    ['Tech Implementation', 30, 'Medium'],
    ['Staff Turnover', 35, 'Medium'],
    ['Payer Contract Changes', 50, 'Low']
]

risk_names = [r[0] for r in risk_data]
risk_prob = [r[1] for r in risk_data]
risk_impact = [3 if r[2]=='High' else 2 if r[2]=='Medium' else 1 for r in risk_data]

# Create risk matrix
risk_matrix = np.array([risk_prob, risk_impact]).T
im = ax4.imshow(risk_matrix, cmap='Reds', aspect='auto')

ax4.set_title('Risk Assessment Matrix', fontsize=14, fontweight='bold')
ax4.set_xlabel('Risk Dimensions')
ax4.set_ylabel('Risk Factors')
ax4.set_xticks([0, 1])
ax4.set_xticklabels(['Probability (%)', 'Impact Level'])
ax4.set_yticks(range(len(risk_names)))
ax4.set_yticklabels(risk_names)

# Add values to heatmap
for i in range(len(risk_names)):
    ax4.text(0, i, f'{risk_prob[i]}%', ha='center', va='center', fontweight='bold', color='white')
    ax4.text(1, i, ['Low', 'Med', 'High'][risk_impact[i]-1], ha='center', va='center', fontweight='bold', color='white')

# 5. Sensitivity Analysis Tornado Chart
ax5 = fig.add_subplot(gs[2, 2:])
base_revenue = financial_summary['Realistic']['annual_collected']
sensitivity_data = {
    'Volume +10%': 10.0,
    'Volume -10%': -10.0,
    'Efficiency +2%': 2.0,
    'Efficiency -2%': -2.0,
    'Avg Claim +5%': 5.0,
    'Avg Claim -5%': -5.0
}

factors = list(sensitivity_data.keys())
impacts = list(sensitivity_data.values())
colors_sens = ['#27ae60' if x > 0 else '#e74c3c' for x in impacts]

bars5 = ax5.barh(factors, impacts, color=colors_sens, alpha=0.7)
ax5.set_title('Revenue Sensitivity Analysis', fontsize=14, fontweight='bold')
ax5.set_xlabel('Revenue Impact (%)')
ax5.grid(True, alpha=0.3, axis='x')
ax5.axvline(x=0, color='black', linestyle='-', alpha=0.8)

# Add value labels
for bar, impact in zip(bars5, impacts):
    width = bar.get_width()
    ax5.text(width + (0.2 if width > 0 else -0.2), bar.get_y() + bar.get_height()/2,
             f'{impact:+.1f}%', ha='left' if width > 0 else 'right', va='center', fontweight='bold')

# 6. Implementation Phase Timeline
ax6 = fig.add_subplot(gs[3, :])
phases = ['Phase 1\n(Months 1-3)', 'Phase 2\n(Months 4-8)', 'Phase 3\n(Months 9-12)']
phase_costs = [150000, 400000, 200000]  # Distributed costs
phase_benefits = [25000, 300000, 450000]  # Cumulative benefits

x_pos = np.arange(len(phases))
width = 0.35

bars6_1 = ax6.bar(x_pos - width/2, phase_costs, width, label='Investment ($)', color='#e74c3c', alpha=0.7)
bars6_2 = ax6.bar(x_pos + width/2, phase_benefits, width, label='Expected Benefits ($)', color='#27ae60', alpha=0.7)

ax6.set_title('Phase-wise Investment vs Expected Benefits', fontsize=14, fontweight='bold')
ax6.set_ylabel('Amount ($)')
ax6.set_xticks(x_pos)
ax6.set_xticklabels(phases)
ax6.legend()
ax6.grid(True, alpha=0.3, axis='y')

# Add value labels
for bar in bars6_1:
    height = bar.get_height()
    ax6.text(bar.get_x() + bar.get_width()/2., height + 5000,
             f'${height/1000:.0f}K', ha='center', va='bottom', fontweight='bold')

for bar in bars6_2:
    height = bar.get_height()
    ax6.text(bar.get_x() + bar.get_width()/2., height + 5000,
             f'${height/1000:.0f}K', ha='center', va='bottom', fontweight='bold')

# 7. Success Metrics Dashboard
ax7 = fig.add_subplot(gs[4, :2])
current_metrics = [64.5, 10.1, 58.3, 150]  # Current efficiency, denial rate, collection rate, AR days
target_metrics = [80, 6, 80, 35]  # Target values
metric_names = ['Collection\nEfficiency (%)', 'Denial\nRate (%)', 'Collection\nRate (%)', 'AR Days']

x_metrics = np.arange(len(metric_names))
width_metrics = 0.35

bars7_1 = ax7.bar(x_metrics - width_metrics/2, current_metrics, width_metrics, 
                  label='Current', color='#95a5a6', alpha=0.7)
bars7_2 = ax7.bar(x_metrics + width_metrics/2, target_metrics, width_metrics, 
                  label='Target', color='#27ae60', alpha=0.7)

ax7.set_title('Current vs Target Performance Metrics', fontsize=14, fontweight='bold')
ax7.set_ylabel('Metric Value')
ax7.set_xticks(x_metrics)
ax7.set_xticklabels(metric_names)
ax7.legend()
ax7.grid(True, alpha=0.3, axis='y')

# Add improvement arrows and percentages
for i, (current, target) in enumerate(zip(current_metrics, target_metrics)):
    improvement = ((target - current) / current) * 100
    color = '#27ae60' if improvement > 0 else '#e74c3c'
    ax7.annotate(f'{improvement:+.1f}%', 
                xy=(i, max(current, target) + max(current_metrics) * 0.05),
                ha='center', va='bottom', fontweight='bold', color=color,
                fontsize=10)

# 8. Revenue Growth Trajectory
ax8 = fig.add_subplot(gs[4, 2:])
months_growth = ['Current', 'Month 6', 'Month 12', 'Year End']
revenue_trajectory = [current_annual_collected/1000000, 120, 135, 148.8]  # in millions

ax8.plot(months_growth, revenue_trajectory, 'o-', color='#3498db', linewidth=3, 
         markersize=10, markerfacecolor='#e74c3c')
ax8.fill_between(months_growth, revenue_trajectory, alpha=0.3, color='#3498db')

ax8.set_title('Revenue Growth Trajectory - Realistic Scenario', fontsize=14, fontweight='bold')
ax8.set_ylabel('Annual Revenue ($M)')
ax8.grid(True, alpha=0.3)

# Add value labels and growth rates
for i, revenue in enumerate(revenue_trajectory):
    ax8.text(i, revenue + 2, f'${revenue:.1f}M', ha='center', va='bottom', fontweight='bold')
    if i > 0:
        growth = ((revenue - revenue_trajectory[0]) / revenue_trajectory[0]) * 100
        ax8.text(i, revenue - 5, f'+{growth:.1f}%', ha='center', va='top', 
                fontweight='bold', color='#27ae60')

# 9. Action Priority Matrix
ax9 = fig.add_subplot(gs[5, :])
actions = [
    'Denial Management System', 'A/R Cleanup Initiative', 'Staff Training Programs',
    'Process Automation', 'Payer Contract Renegotiation', 'Advanced Analytics',
    'Performance Monitoring', 'Quality Improvement'
]
impact_scores = [9, 7, 6, 8, 9, 7, 5, 6]
effort_scores = [8, 6, 4, 7, 9, 8, 3, 5]
priority_colors = ['#e74c3c' if i*e > 56 else '#f39c12' if i*e > 35 else '#27ae60' 
                  for i, e in zip(impact_scores, effort_scores)]

scatter9 = ax9.scatter(effort_scores, impact_scores, s=300, c=priority_colors, alpha=0.7)
ax9.set_title('Action Priority Matrix (Impact vs Effort)', fontsize=14, fontweight='bold')
ax9.set_xlabel('Implementation Effort →')
ax9.set_ylabel('Business Impact →')
ax9.grid(True, alpha=0.3)

# Add quadrant labels
ax9.text(2, 9.5, 'Quick Wins', fontsize=12, fontweight='bold', color='#27ae60')
ax9.text(8, 9.5, 'Major Projects', fontsize=12, fontweight='bold', color='#e74c3c')
ax9.text(2, 2, 'Fill-ins', fontsize=12, fontweight='bold', color='#95a5a6')
ax9.text(8, 2, 'Thankless Tasks', fontsize=12, fontweight='bold', color='#f39c12')

# Add action labels
for i, action in enumerate(actions):
    ax9.annotate(action, (effort_scores[i], impact_scores[i]), 
                xytext=(5, 5), textcoords='offset points', fontsize=9,
                ha='left', va='bottom', fontweight='bold')

plt.suptitle('🎯 STRATEGIC RECOMMENDATIONS DASHBOARD\nCA Hospital Revenue Cycle Management', 
             fontsize=18, fontweight='bold', y=0.98)
plt.tight_layout()
plt.show()

print("\n" + "="*80)
print("STRATEGIC RECOMMENDATIONS VISUALIZATIONS COMPLETE")
print("KEY INSIGHTS FROM DASHBOARD:")
print("• Conservative ROI: Focus on low-risk, immediate improvements")
print("• Realistic ROI: Balanced approach with strong returns expected")
print("• Optimistic ROI: High-risk, high-reward transformation")
print("• Priority: Quick wins in denial management and A/R cleanup")
print("• Timeline: 12-month implementation with quarterly milestones")
print("="*80)