#!/usr/bin/env python3
"""
Script to create a premium, attractive HTML report from Jupyter notebook
"""

import json
import base64
import re
from pathlib import Path

def create_premium_report(notebook_path, output_path):
    """Create an attractive, user-friendly HTML report"""
    
    # Read the notebook
    with open(notebook_path, 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    html_content = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CA Hospital Revenue Cycle Management Analysis</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #2d3748;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .report-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .report-title {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 15px;
        }
        
        .report-subtitle {
            font-size: 1.3rem;
            color: #64748b;
            margin-bottom: 10px;
        }
        
        .report-date {
            font-size: 1rem;
            color: #94a3b8;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .stat-label {
            font-size: 1rem;
            color: #64748b;
            font-weight: 500;
        }
        
        .content-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }
        
        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            font-size: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .section-content {
            padding: 30px;
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            text-align: center;
        }
        
        .chart-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .chart-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .output-text {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9rem;
            line-height: 1.6;
            white-space: pre-wrap;
            overflow-x: auto;
            color: #374151;
        }
        
        .insight-box {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }
        
        .insight-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .insight-list {
            list-style: none;
            padding: 0;
        }
        
        .insight-list li {
            margin: 12px 0;
            padding-left: 25px;
            position: relative;
        }
        
        .insight-list li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #34d399;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .warning-box {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }
        
        .toc {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .toc-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .toc-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .toc-item {
            background: white;
            border-radius: 8px;
            padding: 15px 20px;
            text-decoration: none;
            color: #374151;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .toc-item:hover {
            background: #f3f4f6;
            transform: translateX(5px);
            border-color: #667eea;
        }
        
        .footer {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            text-align: center;
            color: #64748b;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .scroll-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
            z-index: 1000;
        }
        
        .scroll-to-top:hover {
            transform: scale(1.1);
        }
        
        @media (max-width: 768px) {
            .report-title {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .toc-grid {
                grid-template-columns: 1fr;
            }
            
            .main-container {
                padding: 10px;
            }
        }
        
        .loading {
            opacity: 0;
            animation: fadeIn 0.8s ease-in-out forwards;
        }
        
        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="report-header loading">
            <h1 class="report-title">CA Hospital Revenue Cycle Management</h1>
            <p class="report-subtitle">Comprehensive Analysis & Strategic Insights</p>
            <p class="report-date">
                <i class="fas fa-calendar-alt"></i>
                Analysis Period: January 2025 - May 2025
            </p>
        </div>

        <div class="stats-grid loading">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-value">$224.8M</div>
                <div class="stat-label">Total Revenue Analyzed</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-value">59.3%</div>
                <div class="stat-label">Collection Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-value">8.6%</div>
                <div class="stat-label">Claims Denial Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-target"></i>
                </div>
                <div class="stat-value">$32M</div>
                <div class="stat-label">Revenue Opportunity</div>
            </div>
        </div>

        <div class="toc loading">
            <h3 class="toc-title">
                <i class="fas fa-list"></i>
                Report Navigation
            </h3>
            <div class="toc-grid">
                <a href="#overview" class="toc-item">
                    <i class="fas fa-chart-bar"></i>
                    Analysis Overview
                </a>
                <a href="#financial" class="toc-item">
                    <i class="fas fa-coins"></i>
                    Financial Performance
                </a>
                <a href="#claims" class="toc-item">
                    <i class="fas fa-file-medical"></i>
                    Claims Processing
                </a>
                <a href="#denials" class="toc-item">
                    <i class="fas fa-times-circle"></i>
                    Denial Analysis
                </a>
                <a href="#payers" class="toc-item">
                    <i class="fas fa-building"></i>
                    Payer Performance
                </a>
                <a href="#recommendations" class="toc-item">
                    <i class="fas fa-lightbulb"></i>
                    Strategic Recommendations
                </a>
            </div>
        </div>
"""
    
    return html_content

if __name__ == "__main__":
    notebook_path = "Untitled-1.ipynb"
    output_path = "CA_Hospital_Premium_Report.html"
    
    # Start creating the premium report
    html_start = create_premium_report(notebook_path, output_path)
    
    # Write the initial part
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_start)
    
    # Read the notebook and extract content
    with open(notebook_path, 'r', encoding='utf-8') as f:
        notebook = json.load(f)

    # Process notebook content
    content_sections = []
    chart_counter = 0

    for cell in notebook['cells']:
        if cell['cell_type'] == 'code' and cell.get('outputs'):
            for output in cell['outputs']:
                if output.get('output_type') == 'stream':
                    text = ''.join(output.get('text', []))
                    if text.strip() and len(text.strip()) > 20:
                        content_sections.append(('text', text))
                elif output.get('output_type') == 'display_data':
                    if 'data' in output and 'image/png' in output['data']:
                        chart_counter += 1
                        img_data = output['data']['image/png']
                        content_sections.append(('chart', img_data, chart_counter))

    # Add content sections to HTML
    section_titles = [
        ("overview", "Analysis Overview", "fas fa-chart-bar"),
        ("financial", "Financial Performance", "fas fa-coins"),
        ("claims", "Claims Processing", "fas fa-file-medical"),
        ("denials", "Denial Analysis", "fas fa-times-circle"),
        ("payers", "Payer Performance", "fas fa-building"),
        ("recommendations", "Strategic Recommendations", "fas fa-lightbulb")
    ]

    content_html = ""
    section_index = 0
    content_index = 0

    for section_id, section_title, icon in section_titles:
        content_html += f'''
        <div class="content-section loading" id="{section_id}">
            <div class="section-header">
                <i class="{icon}"></i>
                {section_title}
            </div>
            <div class="section-content">
'''

        # Add 2-3 pieces of content per section
        items_per_section = len(content_sections) // len(section_titles)
        for i in range(items_per_section):
            if content_index < len(content_sections):
                content_type, *content_data = content_sections[content_index]

                if content_type == 'text':
                    content_html += f'''
                <div class="output-text">{content_data[0]}</div>
'''
                elif content_type == 'chart':
                    img_data, chart_num = content_data
                    content_html += f'''
                <div class="chart-container">
                    <div class="chart-title">
                        <i class="fas fa-chart-area"></i>
                        Analysis Visualization {chart_num}
                    </div>
                    <img src="data:image/png;base64,{img_data}" alt="Chart {chart_num}">
                </div>
'''
                content_index += 1

        content_html += '''
            </div>
        </div>
'''

    # Add insights and recommendations
    insights_html = '''
        <div class="content-section loading">
            <div class="section-header">
                <i class="fas fa-lightbulb"></i>
                Key Insights & Recommendations
            </div>
            <div class="section-content">
                <div class="insight-box">
                    <div class="insight-title">
                        <i class="fas fa-check-circle"></i>
                        Critical Success Factors
                    </div>
                    <ul class="insight-list">
                        <li><strong>Denial Management:</strong> Implement real-time eligibility verification to reduce "Wrong Payer" denials (20% of total denials)</li>
                        <li><strong>Prior Authorization:</strong> Enhance tracking system to address missing authorization issues (18% of denials)</li>
                        <li><strong>Collection Optimization:</strong> Target 68% collection rate to unlock $19.5M additional revenue</li>
                        <li><strong>Payer Relations:</strong> Leverage Medicare's 91.7% approval rate as benchmark for other payers</li>
                    </ul>
                </div>

                <div class="warning-box">
                    <div class="insight-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        Immediate Action Required
                    </div>
                    <ul class="insight-list">
                        <li>Collection rate of 59.3% is below industry benchmark (65-70%)</li>
                        <li>$91.5M in outstanding accounts receivable needs attention</li>
                        <li>Duplicate claims (16% of denials) indicate system process gaps</li>
                        <li>Coding errors (12% of denials) require staff training enhancement</li>
                    </ul>
                </div>
            </div>
        </div>
'''

    # Footer and JavaScript
    footer_html = '''
        <div class="footer loading">
            <p><strong>Report Generated:</strong> September 9, 2025 | <strong>Data Period:</strong> January - May 2025</p>
            <p><strong>Total Charts:</strong> ''' + str(chart_counter) + ''' visualizations | <strong>Revenue Analyzed:</strong> $224.8 Million</p>
            <p><i class="fas fa-hospital"></i> CA Hospital Revenue Cycle Management System</p>
        </div>
    </div>

    <button class="scroll-to-top" onclick="scrollToTop()">
        <i class="fas fa-arrow-up"></i>
    </button>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Scroll to top function
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Show/hide scroll to top button
        window.addEventListener('scroll', function() {
            const scrollButton = document.querySelector('.scroll-to-top');
            if (window.pageYOffset > 300) {
                scrollButton.style.display = 'block';
            } else {
                scrollButton.style.display = 'none';
            }
        });

        // Animate elements on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all loading elements
        document.querySelectorAll('.loading').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // Initial load animation
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.querySelectorAll('.loading').forEach((el, index) => {
                    setTimeout(() => {
                        el.style.opacity = '1';
                        el.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }, 200);
        });
    </script>
</body>
</html>'''

    # Complete the HTML file
    complete_html = html_start + content_html + insights_html + footer_html

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(complete_html)

    print(f"Successfully created premium report: {output_path}")
    print(f"Included {chart_counter} charts with modern, attractive design")
